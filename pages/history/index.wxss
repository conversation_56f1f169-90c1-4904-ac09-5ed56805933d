/* pages/history/index.wxss */
/**history.wxss**/

/* 页面根元素样式 - 与设计标准保持一致 */
page {
  background-color: #f8f4e9 !important; /* 统一米色背景 */
  height: auto !important;
  min-height: 100vh !important;
  overflow: auto !important;
  position: relative !important;
}

.container {
  min-height: 100vh;
  background-color: #f8f4e9;
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

/* 背景麻将装饰元素 - 与设计标准保持一致 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mahjong-tile {
  position: absolute;
  width: 60rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: rotate(var(--rotation));
  opacity: 0.05; /* 降低透明度，减少视觉干扰 */
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 15%;
  left: 8%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 25%;
  right: 12%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 30%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 20%;
  right: 8%;
}

/* 导航栏操作按钮 - 与 gameList 页面保持一致 */
.nav-action {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 44px;
  height: 44px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-action:active {
  transform: scale(0.95);
  opacity: 0.7;
}

.nav-action.active {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(212, 175, 55, 0.2));
  border-radius: 8rpx;
}

.nav-action.active .filter-icon,
.nav-action.active .action-text {
  color: #d4af37; /* 金色表示活跃状态 */
}

.filter-icon {
  font-size: 32rpx;
  color: #8B0000; /* 统一深红色 */
  margin-bottom: 2rpx;
}

.action-text {
  font-size: 20rpx;
  color: #8B0000; /* 统一深红色 */
  line-height: 1;
}

.action-icon {
  font-size: 28rpx;
  color: #8B0000; /* 统一深红色 */
}

.filter-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: #d4af37;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
}

/* 滚动内容区域 - 参考 gameList 页面的成功案例，优化滚动性能 */
.scroll-content {
  background-color: transparent;
  box-sizing: border-box;
  padding-bottom: 110rpx; /* 为底部导航栏预留空间 */
  /* 滚动性能优化 */
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
  will-change: scroll-position; /* 优化滚动性能 */
  contain: layout style paint; /* 限制重排重绘范围 */
}

.content {
  padding: 32rpx; /* 统一内边距，与 gameList 页面保持一致 */
  padding-bottom: calc(32rpx + 110rpx + env(safe-area-inset-bottom)); /* 底部添加安全区域 */
  margin-top: 0 !important; /* 覆盖全局样式的 margin-top */
  flex: none !important; /* 覆盖全局样式的 flex: 1 */
  background-color: #f8f4e9; /* 统一背景色 */
}

/* 游戏历史区域 - 与 gameList 页面保持相同的白色背景风格 */
.games-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 24rpx;
  color: #333;
}

/* 统一标题样式 - 与设计标准保持一致 */
.mahjong-section-title {
  position: relative;
  display: inline-block;
  padding: 12rpx 20rpx; /* 与设计标准保持一致 */
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 12rpx; /* 统一圆角 */
  color: #8B0000; /* 统一深红色 */
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果 */
  font-size: 28rpx; /* 统一字体大小 */
  margin-bottom: 16rpx;
}

.game-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx; /* 统一间距 */
}

/* 筛选条件指示器 - 确保在固定导航栏下方正确显示 */
.filter-indicator {
  position: fixed;
  top: 88px; /* 导航栏高度 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 24rpx; /* 进一步减少上下内边距 */
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  z-index: 999; /* 确保在内容之上，但在导航栏之下 */
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(212, 175, 55, 0.3);
}

.filter-tag-close {
  margin-left: 8rpx;
  font-size: 24rpx;
  font-weight: bold;
  padding: 4rpx;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-tag-close:active {
  background-color: rgba(139, 0, 0, 0.2);
  transform: scale(0.9);
}

.filter-reset {
  font-size: 24rpx;
  color: #8B0000;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-reset:active {
  background-color: rgba(139, 0, 0, 0.1);
  transform: scale(0.95);
}


/* 游戏卡片 - 与 gameList 页面保持相同的平衡布局风格 */
.game-card {
  background-color: rgba(248, 244, 233, 0.4); /* 统一次要卡片背景 */
  border-radius: 12rpx;
  padding: 28rpx; /* 稍微增加内边距 */
  border: 1rpx solid rgba(212, 175, 55, 0.15); /* 统一边框 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 0; /* 移除底部边距，使用gap */
  display: flex;
  flex-direction: column;
  gap: 16rpx; /* 各行之间的间距 */
  /* 移除复杂的 transition 以提升滚动性能 */
  will-change: transform; /* 启用硬件加速 */
  transform: translateZ(0); /* 强制启用硬件加速 */
}

.game-card:active {
  background-color: rgba(230, 196, 108, 0.25); /* 统一点击效果 */
  border-color: rgba(212, 175, 55, 0.3);
  transform: scale(0.98) translateZ(0); /* 保持硬件加速的点击效果 */
}

/* 游戏卡片头部 - 游戏名称和状态 */
.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4rpx;
}

.game-name {
  font-size: 32rpx; /* 统一标题字体 */
  font-weight: 600;
  color: #333; /* 统一主要文字颜色 */
  flex: 1;
  margin-right: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.game-status {
  display: flex;
  align-items: center;
  gap: 6rpx;
  flex-shrink: 0;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #52c41a; /* 绿色表示进行中 */
  border-radius: 50%;
  display: inline-block;
}

.status-dot.completed {
  background-color: #1890ff; /* 蓝色表示已完成 */
}

.status-text {
  font-size: 22rpx;
  color: #52c41a;
  font-weight: 500;
}

.status-text.completed {
  color: #1890ff; /* 蓝色表示已完成 */
}

/* 游戏卡片中部 - 类型和人数 */
.game-middle {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-type-tag {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: rgba(212, 175, 55, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.3);
  flex-shrink: 0;
}

.type-icon {
  font-size: 24rpx;
}

.type-text {
  font-size: 22rpx;
  color: #8B0000;
  font-weight: 500;
}

.game-players {
  display: flex;
  align-items: center;
  gap: 6rpx;
  flex-shrink: 0;
}

.players-icon {
  font-size: 20rpx;
  color: #666;
}

.players-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 游戏卡片底部 - 时间和操作 */
.game-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8rpx;
  border-top: 1rpx solid rgba(212, 175, 55, 0.1);
}

.game-time {
  display: flex;
  align-items: center;
  gap: 6rpx;
  flex: 1;
}

.time-icon {
  font-size: 20rpx;
  color: #999;
}

.time-text {
  font-size: 22rpx;
  color: #999;
}

.game-action {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.15), rgba(212, 175, 55, 0.15));
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.3);
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.game-action:active {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.3), rgba(212, 175, 55, 0.3));
  transform: scale(0.95);
}

.action-text {
  font-size: 22rpx;
  color: #8B0000;
  font-weight: 500;
}

.action-icon {
  font-size: 24rpx;
  color: #8B0000;
  font-weight: 600;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #666; /* 统一次要文字颜色 */
  font-size: 26rpx; /* 统一正文字体 */
}

/* 没有更多数据 - 隐藏显示 */
.no-more {
  display: none; /* 隐藏"没有更多数据了"提示 */
}

.load-btn {
  color: #8B0000; /* 统一深红色 */
  font-size: 26rpx; /* 统一正文字体 */
  padding: 20rpx 40rpx;
  border: 1rpx solid #d4af37; /* 统一金色边框 */
  border-radius: 16rpx;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(212, 175, 55, 0.2));
}

.load-btn:active {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  transform: scale(0.98); /* 统一点击效果 */
}

.loading-text {
  color: #666; /* 统一次要文字颜色 */
  font-size: 28rpx; /* 统一副标题字体 */
}

/* 空状态 - 应用统一设计标准 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx; /* 统一内边距 */
  text-align: center;
}

.empty-icon {
  font-size: 100rpx; /* 调整图标大小 */
  margin-bottom: 24rpx;
  opacity: 0.5; /* 降低透明度 */
}

.empty-text {
  font-size: 28rpx; /* 统一副标题字体 */
  color: #8B0000; /* 统一深红色 */
  margin-bottom: 12rpx;
  font-weight: 600;
}

.empty-subtext {
  font-size: 24rpx; /* 统一说明文字字体 */
  color: #666; /* 统一次要文字颜色 */
  text-align: center;
}

/* 加载中状态 - 统一样式 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

/* 筛选面板 - 应用统一设计标准 */
.filter-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4); /* 降低透明度 */
  z-index: 1000;
}

.filter-panel {
  position: fixed;
  top: 0;
  right: -75%; /* 调整宽度 */
  width: 75%;
  height: 100%;
  background-color: #ffffff;
  z-index: 1001;
  box-shadow: -4rpx 0 32rpx rgba(0, 0, 0, 0.15); /* 统一阴影效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 统一过渡效果 */
  display: flex;
  flex-direction: column;
}

.filter-panel.show {
  right: 0;
}

.filter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 28rpx; /* 调整内边距 */
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.15); /* 统一边框颜色 */
  background: linear-gradient(135deg, #e6c46c, #d4af37);
}

.filter-panel-title {
  font-size: 28rpx; /* 统一副标题字体 */
  font-weight: 500;
  color: #8B0000; /* 统一深红色 */
}

.filter-panel-close {
  font-size: 36rpx; /* 调整图标大小 */
  color: #8B0000; /* 统一深红色 */
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 52rpx;
  height: 52rpx;
}

.filter-panel-close:active {
  background-color: rgba(139, 0, 0, 0.1);
  transform: scale(0.9);
}

.filter-section {
  padding: 28rpx; /* 统一内边距 */
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.15); /* 统一边框颜色 */
}

.filter-section-title {
  font-size: 28rpx; /* 统一副标题字体 */
  font-weight: 500;
  margin-bottom: 16rpx;
  color: #333; /* 统一主要文字颜色 */
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx; /* 统一间距 */
}

.filter-option {
  padding: 16rpx 28rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.3); /* 统一边框颜色 */
  border-radius: 20rpx;
  font-size: 26rpx; /* 统一正文字体 */
  color: #666; /* 统一次要文字颜色 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.filter-option:active {
  transform: scale(0.98);
  background-color: rgba(212, 175, 55, 0.1);
}

.filter-option.active {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-color: #d4af37; /* 统一金色边框 */
  color: #8B0000; /* 统一深红色 */
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25);
}

.filter-date-range {
  display: flex;
  align-items: center;
  gap: 16rpx; /* 统一间距 */
}

.date-picker {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.3); /* 统一边框颜色 */
  border-radius: 12rpx;
  font-size: 26rpx; /* 统一正文字体 */
  color: #666; /* 统一次要文字颜色 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.date-picker.has-value {
  color: #333; /* 统一主要文字颜色 */
  border-color: #d4af37; /* 统一金色边框 */
  background-color: rgba(230, 196, 108, 0.1);
}

.date-separator {
  color: #666; /* 统一次要文字颜色 */
}

.filter-actions {
  padding: 28rpx; /* 统一内边距 */
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  gap: 16rpx; /* 统一间距 */
}

.btn-reset, .btn-apply {
  flex: 1;
  font-size: 26rpx; /* 统一正文字体 */
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 16rpx; /* 统一圆角 */
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-reset {
  background-color: #f5f5f5;
  color: #666; /* 统一次要文字颜色 */
}

.btn-reset:active {
  background-color: #e8e8e8;
  transform: scale(0.98);
}

.btn-apply {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000; /* 统一深红色 */
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.25); /* 统一阴影效果 */
}

.btn-apply:active {
  background: linear-gradient(135deg, #d4af37, #c19b26);
  transform: scale(0.98);
}

/* 响应式设计 - 与设计标准保持一致 */
@media screen and (max-width: 600rpx) {
  .content {
    padding: 20rpx;
    padding-top: 12rpx;
  }

  .games-section {
    padding: 28rpx;
  }

  .game-card {
    padding: 24rpx;
    gap: 12rpx;
  }

  .game-name {
    font-size: 28rpx;
  }

  .status-text {
    font-size: 20rpx;
  }

  .type-text {
    font-size: 20rpx;
  }

  .players-text {
    font-size: 22rpx;
  }

  .time-text {
    font-size: 20rpx;
  }

  .action-text {
    font-size: 20rpx;
  }

  .action-icon {
    font-size: 22rpx;
  }

  .game-type-tag {
    padding: 6rpx 12rpx;
  }

  .game-action {
    padding: 6rpx 12rpx;
  }

  .empty-icon {
    font-size: 80rpx;
  }

  .empty-text {
    font-size: 24rpx;
  }

  .empty-subtext {
    font-size: 22rpx;
  }

  .filter-panel {
    width: 85%;
    right: -85%;
  }

  .filter-section {
    padding: 24rpx;
  }

  .filter-option {
    font-size: 24rpx;
    padding: 14rpx 24rpx;
  }

  .date-picker {
    font-size: 24rpx;
    padding: 18rpx;
  }

  .btn-reset, .btn-apply {
    height: 72rpx;
    line-height: 72rpx;
    font-size: 24rpx;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 1000rpx) {
  .content {
    max-width: 800rpx;
    margin: 0 auto;
    padding: 40rpx;
  }

  .games-section {
    padding: 40rpx;
  }

  .game-card {
    padding: 36rpx;
    gap: 20rpx;
  }
  
  .game-name {
    font-size: 36rpx;
  }
  
  .status-text {
    font-size: 24rpx;
  }
  
  .type-text {
    font-size: 24rpx;
  }
  
  .players-text {
    font-size: 26rpx;
  }
  
  .time-text {
    font-size: 24rpx;
  }
  
  .action-text {
    font-size: 24rpx;
  }

  .filter-panel {
    width: 60%;
    right: -60%;
  }
}