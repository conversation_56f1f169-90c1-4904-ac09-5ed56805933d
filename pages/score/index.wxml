<!--pages/score/index.wxml-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>

  <!-- 自定义导航栏 -->
  <custom-navbar
    title="{{game ? game.name : '记分'}}"
    show-back="{{true}}"
    show-home="{{true}}"
    bind:height="onNavbarHeight"
  >
    <!-- 右侧操作按钮 -->
    <view slot="right" class="nav-action" bindtap="showOptions">
      <text class="action-icon">⋮</text>
    </view>
  </custom-navbar>

  <!-- 内容区域 -->
  <scroll-view class="score-scroll-view" scroll-y="true" style="height: calc(100vh - {{navbarHeight}}px); margin-top: {{navbarHeight}}px;">
    <view class="content">
      <!-- 玩家分数卡片 -->
      <view class="player-section">
        <view class="form-title">
          <view class="mahjong-section-title">玩家列表</view>
          <view class="multi-select-control">
            <text class="multi-select-label">多选模式</text>
            <view class="switch-container" catchtap="toggleMultiSelect" hover-class="switch-hover" hover-stay-time="100">
              <view class="switch {{multiSelectMode ? 'switch-on' : 'switch-off'}}">
                <view class="switch-handle {{multiSelectMode ? 'handle-on' : 'handle-off'}}"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="game-types" wx:if="{{game && game.players && game.players.length > 0}}">
          <view class="game-type-item {{playerSelectedMap[index] ? 'active' : ''}}"
                wx:for="{{game.players}}"
                wx:key="id"
                bindtap="selectPlayer"
                data-index="{{index}}">

            <view class="type-icon" style="background-color: {{item.color}}; color: #ffffff; width: 80rpx; height: 80rpx; border-radius: 16rpx; display: flex; justify-content: center; align-items: center; box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15); margin-bottom: 12rpx;">
              {{item.name[0]}}
            </view>
            <view class="player-name-container">
              <text class="type-name">{{item.name}}</text>
              <view class="player-badges">
                <text class="player-badge creator" wx:if="{{item.isCreator}}">创建人</text>
                <text class="player-badge invited" wx:if="{{item.isInvited}}">新加入</text>
              </view>
            </view>
            <text class="player-score {{item.score > initialScore ? 'positive' : (item.score < initialScore ? 'negative' : '')}}">
              {{item.score}}
            </text>
          </view>

          <!-- 邀请玩家按钮 -->
          <view class="add-player-item" bindtap="showInvitePlayerDialog">
            <view class="add-player-icon">
              <image class="add-icon-image" src="/static/images/addingPlayer.png" mode="aspectFit"></image>
            </view>
            <view class="add-player-text">
              <text class="add-player-line">邀请</text>
              <text class="add-player-line">玩家</text>
            </view>
          </view>
        </view>
        <view class="empty-state" wx:else>
          <view class="empty-icon">🀄</view>
          <text class="empty-text">无玩家数据</text>
          <button class="btn btn-primary" bindtap="reloadGame">重新加载</button>
        </view>
      </view>

      <!-- 快捷分数按钮 -->
      <view class="quick-score-section">
        <view class="mahjong-section-title">快速记分</view>
        <view class="quick-score-buttons">
          <view class="quick-score-row">
            <view class="quick-btn {{(currentMode === 'win' || (currentMode === 'custom' && customDirection === 'win')) ? 'active' : ''}}" bindtap="setScoreMode" data-mode="win">赢</view>
            <view class="quick-btn {{(currentMode === 'lose' || (currentMode === 'custom' && customDirection === 'lose')) ? 'active' : ''}}" bindtap="setScoreMode" data-mode="lose">输</view>
            <view class="quick-btn {{currentMode === 'custom' ? 'active' : ''}}" bindtap="setScoreMode" data-mode="custom">自定义</view>
          </view>

          <view class="quick-score-row" wx:if="{{currentMode !== 'custom'}}">
            <view class="score-chip {{selectedPlayers.length === 0 ? 'disabled' : ''}}"
                  wx:for="{{scoreChips}}"
                  wx:key="*this"
                  bindtap="applyQuickScore"
                  data-score="{{item}}">
              {{item}}
            </view>
          </view>

          <!-- 自定义分数输入 -->
          <view class="custom-score" wx:if="{{currentMode === 'custom'}}">
            <view class="custom-score-input {{selectedPlayers.length === 0 ? 'disabled' : ''}}">
              <view class="score-input-container">
                <view class="score-decrement" bindtap="decrementScore">-</view>
                <input class="score-input"
                       type="number"
                       value="{{customScore || '1'}}"
                       bindinput="onCustomScoreInput"
                       disabled="{{selectedPlayers.length === 0}}" />
                <view class="score-increment" bindtap="incrementScore">+</view>
              </view>
              <view class="input-btn {{(customScore === '' || customScore === '0') || selectedPlayers.length === 0 ? 'disabled' : ''}}"
                    bindtap="applyCustomScore">
                应用
              </view>
            </view>
          </view>
        </view>
      </view>


      <!-- 本局得分预览 -->
      <view class="round-preview" wx:if="{{hasScoreChanges}}">
        <view class="preview-header">
          <text class="preview-title">本局得分</text>
          <text class="preview-action" bindtap="resetCurrentRound">重置</text>
        </view>
        <view class="preview-scores-compact">
          <view class="preview-item-compact" wx:for="{{currentRoundScores}}" wx:key="index" wx:if="{{item !== 0}}">
            <view class="preview-avatar-compact" style="background-color: {{game.players[index].color}}">
              {{game.players[index].name[0]}}
            </view>
            <text class="preview-name-compact">{{game.players[index].name}}</text>
            <text class="preview-score-compact {{item > 0 ? 'positive' : 'negative'}}">
              {{item > 0 ? '+' : ''}}{{item}}
            </text>
          </view>
        </view>
      </view>

      <!-- 历史记录 -->
      <view class="history-section" wx:if="{{game && game.rounds && game.rounds.length > 0}}">
        <view class="history-header">
          <view class="mahjong-section-title">历史记录 ({{game.rounds.length}}局)</view>
          <view class="collapse-all-control">
            <text class="collapse-all-label">一键折叠</text>
            <view class="switch-container" catchtap="toggleAllRoundsCollapse" hover-class="switch-hover" hover-stay-time="100">
              <view class="switch {{allRoundsCollapsed ? 'switch-on' : 'switch-off'}}">
                <view class="switch-handle {{allRoundsCollapsed ? 'handle-on' : 'handle-off'}}"></view>
              </view>
            </view>
          </view>
        </view>

        <view class="round-list">
          <view class="round-item" wx:for="{{game.rounds}}" wx:key="index">
            <view class="round-header">
              <view class="round-header-left">
                <text class="round-title">第 {{game.rounds.length - index}} 局</text>
                <text class="round-time">{{item.timeFormatted}}</text>
              </view>
              <view class="round-collapse-btn" bindtap="toggleRoundCollapse" data-index="{{index}}">
                <text class="collapse-icon {{item.collapsed ? 'collapsed' : 'expanded'}}">▼</text>
              </view>
            </view>

            <!-- 该局分数详情 -->
            <view class="round-scores-grid {{item.collapsed ? 'collapsed' : 'expanded'}}">
              <view class="round-score-grid-item" wx:for="{{item.scores}}" wx:for-item="score" wx:for-index="playerIndex" wx:key="playerIndex" wx:if="{{score !== 0}}">
                <view class="grid-player-info">
                  <view class="grid-player-avatar" style="background-color: {{game.players[playerIndex].color}}">
                    {{game.players[playerIndex].name[0]}}
                  </view>
                  <text class="grid-player-name">{{game.players[playerIndex].name}}</text>
                </view>
                <text class="grid-score-value {{score > 0 ? 'positive' : 'negative'}}">
                  {{score > 0 ? '+' : ''}}{{score}}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-space"></view>
    </view>
  </scroll-view>

  <!-- 固定底部导航栏 -->
  <view class="fixed-bottom-nav">
    <!-- 左侧区域：总计分数信息 -->
    <view class="nav-left">
      <view class="bottom-left-info" wx:if="{{hasScoreChanges}}">
        <text class="bottom-total-score {{scoreBalanced ? 'balanced' : 'unbalanced'}}">
          总计: {{totalRoundScore}}
        </text>
        <text class="bottom-hint" wx:if="{{!scoreBalanced}}">分数不平衡</text>
      </view>
    </view>
    
    <!-- 中间区域：结束游戏按钮（始终显示） -->
    <view class="nav-center">
      <button class="end-game-btn-center" bindtap="showEndGameConfirm">结束本次游戏</button>
    </view>
    
    <!-- 右侧区域：提交按钮 -->
    <view class="nav-right">
      <button class="bottom-submit-btn {{!scoreBalanced ? 'warning' : ''}}" 
              wx:if="{{hasScoreChanges}}"
              bindtap="submitScores"
              disabled="{{!hasScoreChanges}}">
        {{scoreBalanced ? '提交分数' : '强制提交'}}
      </button>
    </view>
  </view>

  <!-- 选项菜单弹窗 -->
  <view class="options-overlay" wx:if="{{showOptionsMenu}}" bindtap="hideOptions">
    <view class="options-menu" catchtap="stopPropagation">
      <view class="options-header">
        <text class="options-title">游戏选项</text>
        <text class="options-close" bindtap="hideOptions">×</text>
      </view>
      <view class="options-list">
        <view class="option-item" bindtap="navigateToHistory">
          <text class="option-icon">📊</text>
          <text class="option-text">查看详细历史</text>
        </view>
        <view class="option-item" bindtap="showEndGameConfirm">
          <text class="option-icon">🏁</text>
          <text class="option-text">结束游戏</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 局详情弹窗 -->
  <view class="round-detail-overlay" wx:if="{{showRoundDetail}}" bindtap="hideRoundDetail">
    <view class="round-detail-card" catchtap="stopPropagation">
      <view class="detail-header">
        <text class="detail-title">第 {{game.rounds.length - selectedRound}} 局详情</text>
        <text class="detail-close" bindtap="hideRoundDetail">×</text>
      </view>
      <view class="detail-content">
        <view class="detail-time">{{game.rounds[selectedRound].timeFormatted}}</view>
        <view class="detail-scores">
          <view class="detail-score-item" wx:for="{{game.players}}" wx:key="id" wx:for-index="playerIndex">
            <view class="detail-player">
              <view class="detail-avatar" style="background-color: {{item.color}}">
                {{item.name[0]}}
              </view>
              <text class="detail-name">{{item.name}}</text>
            </view>
            <text class="detail-score {{game.rounds[selectedRound].scores[playerIndex] > 0 ? 'positive' : (game.rounds[selectedRound].scores[playerIndex] < 0 ? 'negative' : '')}}">
              {{game.rounds[selectedRound].scores[playerIndex] > 0 ? '+' : ''}}{{game.rounds[selectedRound].scores[playerIndex]}}
            </text>
          </view>
        </view>
        <view class="detail-actions">
          <button class="btn btn-outline" bindtap="hideRoundDetail">关闭</button>
          <button class="btn btn-primary" bindtap="deleteRound" data-index="{{selectedRound}}">删除此局</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 邀请玩家分享弹窗 -->
  <view class="invite-player-modal {{showInvitePlayerModal ? 'show' : ''}}" wx:if="{{showInvitePlayerModal}}" catchtap="hideInvitePlayerDialog">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header" catchtap="stopPropagation">
        <text class="modal-title">邀请玩家加入游戏</text>
        <view class="modal-close" bindtap="hideInvitePlayerDialog">
          <text class="close-icon">×</text>
        </view>
      </view>

      <view class="modal-body" catchtap="stopPropagation">
        <!-- 游戏信息展示 -->
        <view class="game-info-section" catchtap="stopPropagation">
          <view class="game-info-title">游戏信息</view>
          <view class="game-info-item">
            <text class="info-label">游戏名称：</text>
            <text class="info-value">{{game.name}}</text>
          </view>
          <view class="game-info-item">
            <text class="info-label">游戏类型：</text>
            <text class="info-value">{{gameTypeName}}</text>
          </view>
          <view class="game-info-item">
            <text class="info-label">当前人数：</text>
            <text class="info-value">{{game.players.length}}/{{game.playerCount}}</text>
          </view>
        </view>

        <!-- 邀请链接 -->
        <view class="invite-link-section" catchtap="stopPropagation">
          <view class="invite-link-title">邀请链接</view>
          <view class="invite-link-container">
            <view class="invite-link-text">{{inviteLink}}</view>
            <button class="copy-link-btn" bindtap="copyInviteLink" catchtap="stopPropagation">复制</button>
          </view>
          <view class="invite-code-info">
            <text class="invite-code-label">邀请码：</text>
            <text class="invite-code-value">{{inviteCode}}</text>
          </view>
        </view>

        <!-- 分享方式 -->
        <view class="share-methods-section" catchtap="stopPropagation">
          <view class="share-methods-title">分享方式</view>

          <!-- 微信分享按钮 -->
          <button class="share-method-btn wechat-share" open-type="share" bindtap="triggerWechatShare" catchtap="stopPropagation">
            <view class="share-method-icon">💬</view>
            <view class="share-method-content">
              <text class="share-method-name">微信分享</text>
              <text class="share-method-desc">分享到群聊或好友</text>
            </view>
          </button>

          <!-- 复制链接按钮 -->
          <button class="share-method-btn copy-share" bindtap="copyInviteLink" catchtap="stopPropagation">
            <view class="share-method-icon">📋</view>
            <view class="share-method-content">
              <text class="share-method-name">复制链接</text>
              <text class="share-method-desc">复制邀请链接分享</text>
            </view>
          </button>
        </view>

        <!-- 分享预览 -->
        <view class="share-preview-section" catchtap="stopPropagation">
          <view class="share-preview-title">分享预览</view>
          <view class="share-preview-card">
            <view class="preview-content">
              <view class="preview-game-name">邀请你加入「{{game.name}}」</view>
              <view class="preview-desc">{{gameTypeName}}游戏 · {{game.players.length}}/{{game.playerCount}}人</view>
              <view class="preview-status" wx:if="{{game.rounds && game.rounds.length > 0}}">
                已进行{{game.rounds.length}}轮
              </view>
              <view class="preview-status" wx:else>
                等待开始
              </view>
            </view>
            <image class="preview-logo" src="/static/images/logo.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>

      <view class="modal-footer" catchtap="stopPropagation">
        <button class="btn btn-cancel" bindtap="hideInvitePlayerDialog">关闭</button>
      </view>
    </view>
  </view>
</view>