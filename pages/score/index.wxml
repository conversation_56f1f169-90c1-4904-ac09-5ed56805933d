<!--pages/score/index.wxml-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>

  <!-- 自定义导航栏 -->
  <custom-navbar
    title="{{game ? game.name : '记分'}}"
    show-back="{{true}}"
    show-home="{{true}}"
    bind:height="onNavbarHeight"
  >
    <!-- 右侧操作按钮 -->
    <view slot="right" class="nav-action" bindtap="showOptions">
      <text class="action-icon">⋮</text>
    </view>
  </custom-navbar>

  <!-- 内容区域 -->
  <scroll-view class="score-scroll-view" scroll-y="true" style="height: calc(100vh - {{navbarHeight}}px); margin-top: {{navbarHeight}}px;">
    <view class="content">
      <!-- 邀请模式提示 -->
      <view class="invite-mode-tip" wx:if="{{gameMode === 'invite'}}">
        <view class="tip-icon">📱</view>
        <view class="tip-content">
          <text class="tip-title">邀请模式</text>
          <text class="tip-desc">每位玩家都可以自行操作记分</text>
        </view>
      </view>

      <!-- 玩家分数卡片 -->
      <view class="player-section">
        <view class="form-title">
          <view class="mahjong-section-title">玩家列表</view>
          <view class="multi-select-control">
            <text class="multi-select-label">多选模式</text>
            <view class="switch-container" catchtap="toggleMultiSelect" hover-class="switch-hover" hover-stay-time="100">
              <view class="switch {{multiSelectMode ? 'switch-on' : 'switch-off'}}">
                <view class="switch-handle {{multiSelectMode ? 'handle-on' : 'handle-off'}}"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="game-types" wx:if="{{game && game.players && game.players.length > 0}}">
          <view class="game-type-item {{playerSelectedMap[index] ? 'active' : ''}}"
                wx:for="{{game.players}}"
                wx:key="id"
                bindtap="selectPlayer"
                data-index="{{index}}">

            <view class="type-icon" style="background-color: {{item.color}}; color: #ffffff; width: 80rpx; height: 80rpx; border-radius: 16rpx; display: flex; justify-content: center; align-items: center; box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15); margin-bottom: 12rpx;">
              {{item.name[0]}}
            </view>
            <text class="type-name">{{item.name}}</text>
            <text class="player-score {{item.score > initialScore ? 'positive' : (item.score < initialScore ? 'negative' : '')}}">
              {{item.score}}
            </text>
          </view>

          <!-- 添加玩家按钮 -->
          <view class="add-player-item" bindtap="showAddPlayerDialog">
            <view class="add-player-icon">
              <image class="add-icon-image" src="/static/images/addingPlayer.png" mode="aspectFit"></image>
            </view>
            <view class="add-player-text">
              <text class="add-player-line">添加</text>
              <text class="add-player-line">玩家</text>
            </view>
          </view>
        </view>
        <view class="empty-state" wx:else>
          <view class="empty-icon">🀄</view>
          <text class="empty-text">无玩家数据</text>
          <button class="btn btn-primary" bindtap="reloadGame">重新加载</button>
        </view>
      </view>

      <!-- 快捷分数按钮 -->
      <view class="quick-score-section">
        <view class="mahjong-section-title">快速记分</view>
        <view class="quick-score-buttons">
          <view class="quick-score-row">
            <view class="quick-btn {{(currentMode === 'win' || (currentMode === 'custom' && customDirection === 'win')) ? 'active' : ''}}" bindtap="setScoreMode" data-mode="win">赢</view>
            <view class="quick-btn {{(currentMode === 'lose' || (currentMode === 'custom' && customDirection === 'lose')) ? 'active' : ''}}" bindtap="setScoreMode" data-mode="lose">输</view>
            <view class="quick-btn {{currentMode === 'custom' ? 'active' : ''}}" bindtap="setScoreMode" data-mode="custom">自定义</view>
          </view>

          <view class="quick-score-row" wx:if="{{currentMode !== 'custom'}}">
            <view class="score-chip {{selectedPlayers.length === 0 ? 'disabled' : ''}}"
                  wx:for="{{scoreChips}}"
                  wx:key="*this"
                  bindtap="applyQuickScore"
                  data-score="{{item}}">
              {{item}}
            </view>
          </view>

          <!-- 自定义分数输入 -->
          <view class="custom-score" wx:if="{{currentMode === 'custom'}}">
            <view class="custom-score-input {{selectedPlayers.length === 0 ? 'disabled' : ''}}">
              <view class="score-input-container">
                <view class="score-decrement" bindtap="decrementScore">-</view>
                <input class="score-input"
                       type="number"
                       value="{{customScore || '1'}}"
                       bindinput="onCustomScoreInput"
                       disabled="{{selectedPlayers.length === 0}}" />
                <view class="score-increment" bindtap="incrementScore">+</view>
              </view>
              <view class="input-btn {{(customScore === '' || customScore === '0') || selectedPlayers.length === 0 ? 'disabled' : ''}}"
                    bindtap="applyCustomScore">
                应用
              </view>
            </view>
          </view>
        </view>
      </view>


      <!-- 本局得分预览 -->
      <view class="round-preview" wx:if="{{hasScoreChanges}}">
        <view class="preview-header">
          <text class="preview-title">本局得分</text>
          <text class="preview-action" bindtap="resetCurrentRound">重置</text>
        </view>
        <view class="preview-scores-compact">
          <view class="preview-item-compact" wx:for="{{currentRoundScores}}" wx:key="index" wx:if="{{item !== 0}}">
            <view class="preview-avatar-compact" style="background-color: {{game.players[index].color}}">
              {{game.players[index].name[0]}}
            </view>
            <text class="preview-name-compact">{{game.players[index].name}}</text>
            <text class="preview-score-compact {{item > 0 ? 'positive' : 'negative'}}">
              {{item > 0 ? '+' : ''}}{{item}}
            </text>
          </view>
        </view>
      </view>

      <!-- 历史记录 -->
      <view class="history-section" wx:if="{{game && game.rounds && game.rounds.length > 0}}">
        <view class="history-header">
          <view class="mahjong-section-title">历史记录 ({{game.rounds.length}}局)</view>
          <view class="collapse-all-control">
            <text class="collapse-all-label">一键折叠</text>
            <view class="switch-container" catchtap="toggleAllRoundsCollapse" hover-class="switch-hover" hover-stay-time="100">
              <view class="switch {{allRoundsCollapsed ? 'switch-on' : 'switch-off'}}">
                <view class="switch-handle {{allRoundsCollapsed ? 'handle-on' : 'handle-off'}}"></view>
              </view>
            </view>
          </view>
        </view>

        <view class="round-list">
          <view class="round-item" wx:for="{{game.rounds}}" wx:key="index">
            <view class="round-header">
              <view class="round-header-left">
                <text class="round-title">第 {{game.rounds.length - index}} 局</text>
                <text class="round-time">{{item.timeFormatted}}</text>
              </view>
              <view class="round-collapse-btn" bindtap="toggleRoundCollapse" data-index="{{index}}">
                <text class="collapse-icon {{item.collapsed ? 'collapsed' : 'expanded'}}">▼</text>
              </view>
            </view>

            <!-- 该局分数详情 -->
            <view class="round-scores-grid {{item.collapsed ? 'collapsed' : 'expanded'}}">
              <view class="round-score-grid-item" wx:for="{{item.scores}}" wx:for-item="score" wx:for-index="playerIndex" wx:key="playerIndex" wx:if="{{score !== 0}}">
                <view class="grid-player-info">
                  <view class="grid-player-avatar" style="background-color: {{game.players[playerIndex].color}}">
                    {{game.players[playerIndex].name[0]}}
                  </view>
                  <text class="grid-player-name">{{game.players[playerIndex].name}}</text>
                </view>
                <text class="grid-score-value {{score > 0 ? 'positive' : 'negative'}}">
                  {{score > 0 ? '+' : ''}}{{score}}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-space"></view>
    </view>
  </scroll-view>

  <!-- 固定底部导航栏 -->
  <view class="fixed-bottom-nav">
    <!-- 左侧区域：总计分数信息 -->
    <view class="nav-left">
      <view class="bottom-left-info" wx:if="{{hasScoreChanges}}">
        <text class="bottom-total-score {{scoreBalanced ? 'balanced' : 'unbalanced'}}">
          总计: {{totalRoundScore}}
        </text>
        <text class="bottom-hint" wx:if="{{!scoreBalanced}}">分数不平衡</text>
      </view>
    </view>
    
    <!-- 中间区域：结束游戏按钮（始终显示） -->
    <view class="nav-center">
      <button class="end-game-btn-center" bindtap="showEndGameConfirm">结束本次游戏</button>
    </view>
    
    <!-- 右侧区域：提交按钮 -->
    <view class="nav-right">
      <button class="bottom-submit-btn {{!scoreBalanced ? 'warning' : ''}}" 
              wx:if="{{hasScoreChanges}}"
              bindtap="submitScores"
              disabled="{{!hasScoreChanges}}">
        {{scoreBalanced ? '提交分数' : '强制提交'}}
      </button>
    </view>
  </view>

  <!-- 选项菜单弹窗 -->
  <view class="options-overlay" wx:if="{{showOptionsMenu}}" bindtap="hideOptions">
    <view class="options-menu" catchtap="stopPropagation">
      <view class="options-header">
        <text class="options-title">游戏选项</text>
        <text class="options-close" bindtap="hideOptions">×</text>
      </view>
      <view class="options-list">
        <view class="option-item" bindtap="navigateToHistory">
          <text class="option-icon">📊</text>
          <text class="option-text">查看详细历史</text>
        </view>
        <view class="option-item" bindtap="showEndGameConfirm">
          <text class="option-icon">🏁</text>
          <text class="option-text">结束游戏</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 局详情弹窗 -->
  <view class="round-detail-overlay" wx:if="{{showRoundDetail}}" bindtap="hideRoundDetail">
    <view class="round-detail-card" catchtap="stopPropagation">
      <view class="detail-header">
        <text class="detail-title">第 {{game.rounds.length - selectedRound}} 局详情</text>
        <text class="detail-close" bindtap="hideRoundDetail">×</text>
      </view>
      <view class="detail-content">
        <view class="detail-time">{{game.rounds[selectedRound].timeFormatted}}</view>
        <view class="detail-scores">
          <view class="detail-score-item" wx:for="{{game.players}}" wx:key="id" wx:for-index="playerIndex">
            <view class="detail-player">
              <view class="detail-avatar" style="background-color: {{item.color}}">
                {{item.name[0]}}
              </view>
              <text class="detail-name">{{item.name}}</text>
            </view>
            <text class="detail-score {{game.rounds[selectedRound].scores[playerIndex] > 0 ? 'positive' : (game.rounds[selectedRound].scores[playerIndex] < 0 ? 'negative' : '')}}">
              {{game.rounds[selectedRound].scores[playerIndex] > 0 ? '+' : ''}}{{game.rounds[selectedRound].scores[playerIndex]}}
            </text>
          </view>
        </view>
        <view class="detail-actions">
          <button class="btn btn-outline" bindtap="hideRoundDetail">关闭</button>
          <button class="btn btn-primary" bindtap="deleteRound" data-index="{{selectedRound}}">删除此局</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加玩家弹窗 -->
  <view class="add-player-modal {{showAddPlayerModal ? 'show' : ''}}" wx:if="{{showAddPlayerModal}}" catchtap="hideAddPlayerDialog">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header" catchtap="stopPropagation">
        <text class="modal-title">添加新玩家</text>
        <view class="modal-close" bindtap="hideAddPlayerDialog">
          <text class="close-icon">×</text>
        </view>
      </view>

      <view class="modal-body" catchtap="stopPropagation">
        <view class="form-group" catchtap="stopPropagation">
          <text class="form-label">玩家昵称</text>
          <input class="form-input" placeholder="请输入玩家昵称" value="{{newPlayerName}}" bindinput="onNewPlayerNameInput" catchtap="stopPropagation" />
        </view>

        <view class="form-group" catchtap="stopPropagation">
          <text class="form-label">选择颜色</text>
          <view class="color-selector" catchtap="stopPropagation">
            <view class="color-options" catchtap="stopPropagation">
              <view class="color-option {{selectedColorIndex === index ? 'selected' : ''}}"
                    wx:for="{{colorOptions}}"
                    wx:key="name"
                    style="background-color: {{item.value}}"
                    catchtap="selectColor"
                    data-index="{{index}}">
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="modal-footer" catchtap="stopPropagation">
        <button class="btn btn-cancel" bindtap="hideAddPlayerDialog">取消</button>
        <button class="btn btn-confirm {{canAddNewPlayer ? 'active' : ''}}" bindtap="confirmAddPlayer" disabled="{{!canAddNewPlayer}}">确定</button>
      </view>
    </view>
  </view>
</view>