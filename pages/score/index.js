// pages/score/index.js
// 引入API工具
const api = require('../../utils/api');
// 引入路由工具
const router = require('../../utils/router');

Page({

    /**
     * 页面的初始数据
     */
    data: {
        statusBarHeight: 20,
        navbarHeight: 88, // 自定义导航栏高度
        game: null,
        gameId: '',
        gameMode: 'local', // 游戏模式：local(本地) / invite(邀请好友)
        roundCount: 0,
        initialScore: 0, // 初始分数
        isLoading: false, // 加载状态

        // 玩家选择
        selectedPlayers: [], // 当前选中的玩家索引数组
        currentPlayer: -1, // 当前选中的玩家索引（兼容旧版本）
        playerSelectedMap: {}, // 玩家选中状态映射 {0: true, 1: false, 2: true}
        multiSelectMode: true, // 是否开启多选模式，默认开启

        // 快速记分
        currentMode: 'win', // 默认使用"赢"模式
        scoreChips: [1, 2, 5, 10], // 快速分数选项
        customScore: '1', // 自定义分数值 - 默认为1
        customDirection: '', // 用于标记在自定义模式下选择的方向（'win'或'lose'）

        // 本局得分
        currentRoundScores: [], // 当前局每个玩家的分数变化
        hasScoreChanges: false, // 是否有分数变化
        totalRoundScore: 0, // 当前局分数总和
        scoreBalanced: true, // 分数是否平衡

        // 邀请玩家功能
        showInvitePlayerModal: false, // 是否显示邀请玩家弹窗
        gameTypeName: '', // 游戏类型名称（用于显示）
        inviteLink: '', // 生成的邀请链接
        inviteCode: '', // 当前游戏的邀请码


        // 弹窗控制
        showOptionsMenu: false,
        showRoundDetail: false,
        selectedRound: 0,

        // 历史记录折叠控制
        allRoundsCollapsed: false, // 一键折叠所有记录的状态
        fromHistory: false // 标记是否从历史记录页面进入
    },


    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // 强制设置页面样式，防止跳转时的视觉bug
        this.forcePageStyle();

        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();

        // 恢复一键折叠状态
        let allRoundsCollapsed = false;
        try {
            allRoundsCollapsed = wx.getStorageSync('allRoundsCollapsed') || false;
        } catch (error) {
            // 获取一键折叠状态失败，使用默认值
        }

        // 恢复多选模式状态
        let multiSelectMode = true; // 默认为多选模式
        try {
            const storedMultiSelectMode = wx.getStorageSync('multiSelectMode');
            // 只有当存储中有明确的false值时才设为false
            if (storedMultiSelectMode === false) {
                multiSelectMode = false;
            }
        } catch (error) {
            // 获取多选模式状态失败，使用默认值
        }

        // 确保selectedPlayers初始化为空数组
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            statusBarHeight: systemInfo.statusBarHeight,
            selectedPlayers: [], // 确保初始化为空数组
            currentPlayer: -1, // 重置当前选中玩家
            allRoundsCollapsed: allRoundsCollapsed,
            multiSelectMode: multiSelectMode
        });

        if (options.id) {
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                gameId: options.id
            }, () => {
                // 检查是否是通过邀请链接进入
                if (options.inviteCode && options.from === 'invite') {
                    this.handleInviteJoin(options.inviteCode);
                } else {
                    // 确保gameId设置完成后再加载游戏
                    this.loadGame();
                }
            });
        } else {
            wx.showToast({
                title: '缺少游戏ID',
                icon: 'none'
            });
            setTimeout(() => {
                wx.navigateBack();
            }, 1000);
        }
    },

    // 强制设置页面样式，防止跳转时的视觉bug
    forcePageStyle() {
        try {
            // 强制设置页面背景色
            if (wx.setBackgroundColor) {
                wx.setBackgroundColor({
                    backgroundColor: '#f5f7fa',
                    backgroundColorTop: '#f5f7fa',
                    backgroundColorBottom: '#f5f7fa'
                });
            }
        } catch (error) {
            // 设置页面样式失败，忽略错误
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {
        // 如果游戏对象为空，尝试重新加载
        if (!this.data.game && this.data.gameId) {
            this.loadGame();
        }
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        // 再次强制设置页面样式
        this.forcePageStyle();
        this.checkGameValid();

        // 确保玩家选中状态正确
        this.setData({
            selectedPlayers: this.data.selectedPlayers || [],
            _forceUpdate: Date.now() // 添加时间戳强制更新
        });
    },

    // 检查游戏对象是否有效
    checkGameValid() {
        const {
            game,
            gameId
        } = this.data;

        if (!game && gameId) {
            this.loadGame();
            return;
        }

        if (game && (!game.players || game.players.length === 0)) {
            wx.showToast({
                title: '玩家数据无效',
                icon: 'none'
            });
        }
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
        this.loadGame();
        wx.stopPullDownRefresh();
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    loadGame() {
        return new Promise((resolve, reject) => {
            const {
                gameId
            } = this.data;

            if (this.data.isLoading) {
                reject(new Error('正在加载中'));
                return;
            }

            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                isLoading: true,
                selectedPlayers: [], // 重置选中玩家状态
                currentPlayer: -1 // 重置当前选中玩家
            });

            // 显示加载中提示
            wx.showLoading({
                title: '加载中...',
            });

            // 调用API获取游戏详情
            api.games.detail(gameId).then(res => {
                wx.hideLoading();

                const game = res.data;

                // 检查玩家数据
                if (!game.players || game.players.length === 0) {
                    wx.showToast({
                        title: '游戏没有玩家数据',
                        icon: 'none'
                    });
                    this.setData({
                        _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                        isLoading: false
                    });
                    return;
                }

                // 格式化轮次时间和分数数据
                if (game.rounds && game.rounds.length > 0) {
                    // 按时间倒序排列（最新的在前面）
                    game.rounds.sort((a, b) => new Date(b.time) - new Date(a.time));

                    game.rounds.forEach(round => {
                        const date = new Date(round.time);
                        round.timeFormatted = this.formatTime(date);

                        // 根据一键折叠状态设置折叠状态
                        round.collapsed = this.data.allRoundsCollapsed;

                        // 初始化分数数组
                        let scoresArray = new Array(game.players.length).fill(0);

                        // 处理新的API返回格式，scores是包含playerId和score的对象数组
                        if (round.scores && Array.isArray(round.scores)) {
                            round.scores.forEach(scoreObj => {
                                // 查找playerID对应的索引
                                const playerIndex = game.players.findIndex(player => player.id === scoreObj.playerId);
                                if (playerIndex !== -1) {
                                    scoresArray[playerIndex] = scoreObj.score;
                                }
                            });
                        }

                        // 替换原始scores为处理后的数组
                        round.scores = scoresArray;
                    });
                }

                // 获取初始分数
                const initialScore = game.initialScore || 0;

                // 初始化当前轮次的分数数组
                const currentRoundScores = Array(game.players.length).fill(0);

                // 为玩家添加选中状态属性
                if (game.players && game.players.length > 0) {
                    game.players = game.players.map(player => ({
                        ...player,
                        selected: false // 添加选中状态属性
                    }));
                }

                this.setData({
                    _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                    game: game,
                    initialScore,
                    gameMode: game.mode || 'local', // 设置游戏模式
                    roundCount: game.rounds ? game.rounds.length : 0,
                    currentRoundScores: currentRoundScores,
                    selectedPlayers: [], // 确保初始化为空数组
                    currentPlayer: -1, // 重置当前选中玩家
                    isLoading: false
                });

                resolve(game);

            }).catch(err => {
                // 尝试从本地存储加载游戏数据
                this.loadGameFromLocalStorageWithPromise().then(resolve).catch(reject);
            });
        });
    },

    // 从本地存储加载游戏数据（Promise版本，用于邀请流程）
    loadGameFromLocalStorageWithPromise() {
        return new Promise((resolve, reject) => {
            try {
                const result = this.loadGameFromLocalStorageSync();
                if (result) {
                    resolve(result);
                } else {
                    reject(new Error('游戏数据不存在'));
                }
            } catch (error) {
                reject(error);
            }
        });
    },

    // 从本地存储加载游戏数据（用于邀请模式或API失败时的备选方案）
    loadGameFromLocalStorage() {
        this.loadGameFromLocalStorageSync();
    },

    // 同步版本的本地存储加载方法
    loadGameFromLocalStorageSync() {
        const {
            gameId
        } = this.data;

        try {
            // 从本地存储获取游戏数据
            const games = wx.getStorageSync('games') || [];
            const game = games.find(g => g.id === gameId);

            if (!game) {
                wx.hideLoading();
                wx.showToast({
                    title: '未找到游戏数据',
                    icon: 'none',
                    duration: 2000
                });

                this.setData({
                    _selectionTimeStamp: Date.now(),
                    isLoading: false
                });

                setTimeout(() => {
                    wx.navigateBack();
                }, 1500);
                return null;
            }

            wx.hideLoading();

            // 检查玩家数据
            if (!game.players || game.players.length === 0) {
                wx.showToast({
                    title: '游戏没有玩家数据',
                    icon: 'none'
                });
                this.setData({
                    _selectionTimeStamp: Date.now(),
                    isLoading: false
                });
                return null;
            }

            // 处理轮次数据
            if (game.rounds && game.rounds.length > 0) {
                game.rounds.forEach(round => {
                    if (round.time) {
                        const date = new Date(round.time);
                        round.timeFormatted = this.formatTime(date);
                    }
                    round.collapsed = this.data.allRoundsCollapsed;
                });
            }

            // 获取初始分数
            const initialScore = game.initialScore || 0;

            // 初始化当前轮次的分数数组
            const currentRoundScores = Array(game.players.length).fill(0);

            // 为玩家添加选中状态属性
            if (game.players && game.players.length > 0) {
                game.players = game.players.map(player => ({
                    ...player,
                    selected: false
                }));
            }

            this.setData({
                _selectionTimeStamp: Date.now(),
                game: game,
                initialScore,
                gameMode: game.mode || 'local',
                roundCount: game.rounds ? game.rounds.length : 0,
                currentRoundScores: currentRoundScores,
                selectedPlayers: [],
                currentPlayer: -1,
                isLoading: false
            });

            return game;

        } catch (error) {
            wx.hideLoading();

            wx.showToast({
                title: '加载游戏数据失败',
                icon: 'none',
                duration: 2000
            });

            this.setData({
                _selectionTimeStamp: Date.now(),
                isLoading: false
            });

            setTimeout(() => {
                wx.navigateBack();
            }, 1500);

            return null;
        }
    },

    // 导航栏高度回调
    onNavbarHeight(e) {
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            navbarHeight: e.detail.height
        });
    },

    navigateBack() {
        // 如果是从历史记录页面进入，则返回历史记录页面
        if (this.data.fromHistory) {
            wx.navigateBack();
            return;
        }

        // 否则返回游戏列表页面（Tab页面）
        wx.switchTab({
            url: '/pages/gameList/index'
        }).catch(err => {
            // 如果跳转失败，显示错误提示
            wx.showToast({
                title: '跳转失败: ' + (err.errMsg || '未知错误'),
                icon: 'none',
                duration: 2000
            });
        });
    },

    // 选择玩家（新版本-支持多选）
    // 重构：玩家选择方法 - 简化逻辑，确保状态更新可靠
    selectPlayer(e) {
        const index = parseInt(e.currentTarget.dataset.index);

        // 验证输入参数
        if (isNaN(index) || index < 0) {
            return;
        }

        if (!this.data.game || !this.data.game.players || index >= this.data.game.players.length) {
            return;
        }

        // 获取当前选中状态
        const currentSelected = this.data.selectedPlayers || [];
        const isCurrentlySelected = currentSelected.indexOf(index) !== -1;

        // 计算新的选中状态
        let newSelectedPlayers;

        if (this.data.multiSelectMode) {
            // 多选模式：可以选择多个玩家
            if (isCurrentlySelected) {
                // 取消选中：从数组中移除
                newSelectedPlayers = currentSelected.filter(i => i !== index);
            } else {
                // 选中：添加到数组
                newSelectedPlayers = [...currentSelected, index];
            }
        } else {
            // 单选模式：只能选择一个玩家
            if (isCurrentlySelected) {
                // 取消选中当前玩家
                newSelectedPlayers = [];
            } else {
                // 选中新玩家，取消其他玩家
                newSelectedPlayers = [index];
            }
        }

        // 构建新的选中状态映射
        const newPlayerSelectedMap = {};
        newSelectedPlayers.forEach(i => {
            newPlayerSelectedMap[i] = true;
        });

        // 更新数据并强制刷新
        this.setData({
            selectedPlayers: newSelectedPlayers,
            playerSelectedMap: newPlayerSelectedMap,
            currentPlayer: newSelectedPlayers.length === 1 ? newSelectedPlayers[0] : -1,
            _forceUpdate: Date.now() // 添加时间戳强制更新
        });
    },

    // 切换多选模式
    toggleMultiSelect() {
        const newMultiSelectMode = !this.data.multiSelectMode;

        // 如果从多选切换到单选，且当前选中了多个玩家，则只保留第一个选中的玩家
        let newSelectedPlayers = [...this.data.selectedPlayers];
        let newPlayerSelectedMap = {
            ...this.data.playerSelectedMap
        };

        if (!newMultiSelectMode && newSelectedPlayers.length > 1) {
            const firstSelectedPlayer = newSelectedPlayers[0];
            newSelectedPlayers = [firstSelectedPlayer];

            // 重建选中状态映射
            newPlayerSelectedMap = {};
            newPlayerSelectedMap[firstSelectedPlayer] = true;
        }

        this.setData({
            multiSelectMode: newMultiSelectMode,
            selectedPlayers: newSelectedPlayers,
            playerSelectedMap: newPlayerSelectedMap,
            currentPlayer: newSelectedPlayers.length === 1 ? newSelectedPlayers[0] : -1,
            _forceUpdate: Date.now() // 添加时间戳强制更新
        });

        // 保存多选模式状态到本地存储
        try {
            wx.setStorageSync('multiSelectMode', newMultiSelectMode);
        } catch (error) {
            // 保存多选模式状态失败，忽略错误
        }
    },

    // 设置记分模式
    setScoreMode(e) {
        const mode = e.currentTarget.dataset.mode;
        const {
            currentMode
        } = this.data;

        // 如果是自定义模式下再次点击自定义按钮，返回到win模式
        if (currentMode === 'custom' && mode === 'custom') {
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                currentMode: 'win',
                customDirection: ''
            });
            return;
        }

        // 如果在自定义模式下点击赢或输按钮，只设置方向，不应用分数
        if (currentMode === 'custom' && (mode === 'win' || mode === 'lose')) {
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                customDirection: mode
            });
            return;
        }

        // 其他情况
        if (mode === 'custom') {
            // 切换到自定义模式
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                currentMode: mode,
                customDirection: ''
            });
        } else {
            // 切换到赢/输模式
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                currentMode: mode,
                customScore: '1',
                customDirection: ''
            });
        }
    },

    // 应用快速分数（支持多个玩家）
    applyQuickScore(e) {
        const {
            currentRoundScores,
            game,
            currentMode
        } = this.data;

        // 确保selectedPlayers是一个数组
        const selectedPlayers = Array.isArray(this.data.selectedPlayers) ? this.data.selectedPlayers : [];

        // 检查是否选择了玩家
        if (!selectedPlayers.length) {
            wx.showToast({
                title: '请先选择玩家',
                icon: 'none'
            });
            return;
        }

        if (!game || !game.players || !game.players.length) {
            wx.showToast({
                title: '玩家数据无效',
                icon: 'none'
            });
            return;
        }

        // 获取点击的分数值
        let score = Number(e.currentTarget.dataset.score);

        // 在"输"模式下，将分数转换为负数
        if (currentMode === 'lose') {
            score = -score;
        }

        const updatedScores = [...currentRoundScores];

        // 给所有选中的玩家添加分数
        selectedPlayers.forEach(playerIndex => {
            if (playerIndex >= 0 && playerIndex < game.players.length) {
                updatedScores[playerIndex] += score;
            }
        });

        this.updateCurrentRoundScores(updatedScores);
    },

    // 自定义分数输入
    onCustomScoreInput(e) {
        // 获取输入值，可能是空字符串或数字字符串
        const inputValue = e.detail.value;

        // 不要在这里处理转换，保持原始输入
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            customScore: inputValue
        });
    },

    // 增加自定义分数
    incrementScore() {
        const {
            customScore,
            selectedPlayers
        } = this.data;

        // 如果没有选择玩家，不执行操作
        if (!selectedPlayers || selectedPlayers.length === 0) {
            wx.showToast({
                title: '请先选择玩家',
                icon: 'none'
            });
            return;
        }

        // 处理空字符串或非数字输入，默认为1
        const currentScore = customScore === '' || isNaN(Number(customScore)) ? 1 : Number(customScore);

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            customScore: (currentScore + 1).toString()
        });
    },

    // 减少自定义分数
    decrementScore() {
        const {
            customScore,
            selectedPlayers
        } = this.data;

        // 如果没有选择玩家，不执行操作
        if (!selectedPlayers || selectedPlayers.length === 0) {
            wx.showToast({
                title: '请先选择玩家',
                icon: 'none'
            });
            return;
        }

        // 处理空字符串或非数字输入，默认为1
        const currentScore = customScore === '' || isNaN(Number(customScore)) ? 1 : Number(customScore);

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            customScore: (currentScore - 1).toString()
        });
    },

    // 应用自定义分数（支持多个玩家）
    applyCustomScore() {
        const {
            currentRoundScores,
            customScore,
            customDirection,
            game
        } = this.data;

        // 确保selectedPlayers是一个数组
        const selectedPlayers = Array.isArray(this.data.selectedPlayers) ? this.data.selectedPlayers : [];

        // 检查是否选择了玩家
        if (!selectedPlayers.length) {
            wx.showToast({
                title: '请先选择玩家',
                icon: 'none'
            });
            return;
        }

        // 检查是否选择了方向（赢或输）
        if (!customDirection) {
            wx.showToast({
                title: '请先点击赢或输',
                icon: 'none'
            });
            return;
        }

        // 检查分数是否为空或0
        if (customScore === '' || customScore === '0') {
            wx.showToast({
                title: '请输入有效分数',
                icon: 'none'
            });
            return;
        }

        // 将分数转换为数字
        const scoreNum = Number(customScore);

        // 检查是否为有效数字
        if (isNaN(scoreNum)) {
            wx.showToast({
                title: '请输入有效分数',
                icon: 'none'
            });
            return;
        }

        // 检查游戏和玩家数据有效性
        if (!game || !game.players || !game.players.length) {
            wx.showToast({
                title: '玩家数据无效',
                icon: 'none'
            });
            return;
        }

        // 根据方向计算实际分数（赢为正，输为负）
        const actualScore = scoreNum * (customDirection === 'win' ? 1 : -1);

        // 更新选中的玩家分数
        const updatedScores = [...currentRoundScores];
        selectedPlayers.forEach(playerIndex => {
            if (playerIndex >= 0 && playerIndex < game.players.length) {
                updatedScores[playerIndex] += actualScore;
            }
        });

        this.updateCurrentRoundScores(updatedScores);

        // 清空自定义分数，重置为默认值1，但保持方向选择
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            customScore: '1'
        });

        // 显示提示
        wx.showToast({
            title: '已应用分数',
            icon: 'success',
            duration: 1000
        });
    },

    // 更新当前局分数
    updateCurrentRoundScores(scores) {
        // 计算总分
        let totalScore = 0;
        scores.forEach(score => {
            totalScore += Number(score) || 0;
        });

        const hasScoreChanges = scores.some(score => score !== 0);

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            currentRoundScores: scores,
            totalRoundScore: totalScore,
            scoreBalanced: Math.abs(totalScore) < 0.01, // 考虑浮点数精度问题
            hasScoreChanges
        });
    },

    // 重置当前局
    resetCurrentRound() {
        const {
            game
        } = this.data;
        if (!game) return;

        const currentRoundScores = new Array(game.players.length).fill(0);

        // 只重置分数，不清除玩家选择
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            currentRoundScores,
            totalRoundScore: 0,
            scoreBalanced: true,
            hasScoreChanges: false,
            customScore: '1'
        });
    },

    // 提交分数
    submitScores() {
        const {
            game,
            currentRoundScores,
            totalRoundScore,
            scoreBalanced
        } = this.data;

        if (!game) {
            wx.showToast({
                title: '游戏数据无效',
                icon: 'none'
            });
            return;
        }

        if (!currentRoundScores.some(score => score !== 0)) {
            wx.showToast({
                title: '请先输入分数',
                icon: 'none'
            });
            return;
        }

        if (!scoreBalanced && Math.abs(totalRoundScore) > 0.01) {
            wx.showModal({
                title: '分数不平衡',
                content: `当前总分为${totalRoundScore}，确定要继续提交吗？`,
                success: (res) => {
                    if (res.confirm) {
                        this.doSubmitScores();
                    }
                }
            });
        } else {
            this.doSubmitScores();
        }
    },

    // 实际提交分数（API + 本地存储）
    doSubmitScores() {
        const {
            game,
            currentRoundScores
        } = this.data;

        // 显示加载状态
        wx.showLoading({
            title: '保存中...',
            mask: true
        });

        // 构建API请求数据
        const scoresData = game.players.map((player, index) => ({
            playerId: player.id || player._id || `player_${index}`,
            score: currentRoundScores[index]
        }));

        // 调用API提交分数
        api.games.submitScore(game.id, scoresData).then(res => {
            // 处理API响应，更新本地数据
            this.handleSubmitScoreSuccess(res.data, currentRoundScores);

            wx.hideLoading();
            wx.showToast({
                title: '保存成功',
                icon: 'success'
            });

        }).catch(err => {
            wx.hideLoading();

            // 根据错误类型显示不同的提示
            let errorMessage = '保存失败';
            if (err.code === 400) {
                errorMessage = '参数错误，请检查分数数据';
            } else if (err.code === 401) {
                errorMessage = '登录已过期，请重新登录';
            } else if (err.code === 404) {
                errorMessage = '游戏不存在';
            } else if (err.code === 500) {
                errorMessage = '服务器错误，请稍后重试';
            } else if (err.code === -1) {
                errorMessage = '网络连接失败';
            } else if (err.message) {
                errorMessage = err.message;
            }

            wx.showModal({
                title: '保存失败',
                content: `${errorMessage}\n\n是否保存到本地？`,
                confirmText: '保存到本地',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        // 保存到本地
                        this.saveScoresToLocal(currentRoundScores);
                    }
                }
            });
        });
    },

    // 处理API提交成功的响应
    handleSubmitScoreSuccess(apiData, currentRoundScores) {
        const {
            game
        } = this.data;

        // 创建新的轮次记录
        const newRound = {
            id: apiData.roundId || `round_${Date.now()}`,
            time: new Date(apiData.time || Date.now()).toISOString(),
            timeFormatted: this.formatTime(new Date(apiData.time || Date.now())),
            scores: [...currentRoundScores],
            collapsed: false // 新轮次默认展开
        };

        // 更新游戏数据
        const updatedGame = {
            ...game
        };

        // 初始化 rounds 数组（如果不存在）
        if (!updatedGame.rounds) {
            updatedGame.rounds = [];
        }

        // 将新轮次添加到开头（最新的在前面）
        updatedGame.rounds.unshift(newRound);

        // 如果API返回了更新后的玩家数据，使用API数据；否则本地计算
        if (apiData.players && Array.isArray(apiData.players)) {
            updatedGame.players = apiData.players.map(apiPlayer => {
                const localPlayer = game.players.find(p => p.id === apiPlayer.id);
                return {
                    ...localPlayer,
                    ...apiPlayer,
                    selected: false // 重置选中状态
                };
            });
        } else {
            // 本地计算玩家分数
            updatedGame.players = updatedGame.players.map((player, index) => ({
                ...player,
                score: (player.score || this.data.initialScore) + currentRoundScores[index],
                selected: false // 重置选中状态
            }));
        }

        // 保存到本地存储
        this.saveGameToLocal(updatedGame);

        // 如果添加新轮次后不是所有记录都折叠，则更新一键折叠状态
        const allCollapsed = updatedGame.rounds.every(round => round.collapsed);
        if (!allCollapsed && this.data.allRoundsCollapsed) {
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                allRoundsCollapsed: false
            });
            try {
                wx.setStorageSync('allRoundsCollapsed', false);
            } catch (error) {
                // 保存一键折叠状态失败，忽略错误
            }
        }

        // 更新页面数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: updatedGame,
            selectedPlayers: [], // 重置选中玩家
            currentPlayer: -1
        });

        // 重置当前轮次分数
        this.resetCurrentRound();
    },

    // 保存分数到本地（API失败时的备选方案）
    saveScoresToLocal(currentRoundScores) {
        const {
            game
        } = this.data;

        // 创建新的轮次记录
        const newRound = {
            id: `round_${Date.now()}`,
            time: new Date().toISOString(),
            timeFormatted: this.formatTime(new Date()),
            scores: [...currentRoundScores],
            collapsed: false // 新轮次默认展开
        };

        // 更新游戏数据
        const updatedGame = {
            ...game
        };

        // 初始化 rounds 数组（如果不存在）
        if (!updatedGame.rounds) {
            updatedGame.rounds = [];
        }

        // 将新轮次添加到开头（最新的在前面）
        updatedGame.rounds.unshift(newRound);

        // 更新玩家分数
        updatedGame.players = updatedGame.players.map((player, index) => ({
            ...player,
            score: (player.score || this.data.initialScore) + currentRoundScores[index],
            selected: false // 重置选中状态
        }));

        // 保存到本地存储
        this.saveGameToLocal(updatedGame);

        // 如果添加新轮次后不是所有记录都折叠，则更新一键折叠状态
        const allCollapsed = updatedGame.rounds.every(round => round.collapsed);
        if (!allCollapsed && this.data.allRoundsCollapsed) {
            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                allRoundsCollapsed: false
            });
            try {
                wx.setStorageSync('allRoundsCollapsed', false);
            } catch (error) {
                // 保存一键折叠状态失败，忽略错误
            }
        }

        // 更新页面数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: updatedGame,
            selectedPlayers: [], // 重置选中玩家
            currentPlayer: -1
        });

        // 重置当前轮次分数
        this.resetCurrentRound();

        wx.showToast({
            title: '已保存到本地',
            icon: 'success'
        });
    },

    // 保存游戏数据到本地存储
    saveGameToLocal(gameData) {
        try {
            // 获取本地存储的游戏列表
            const localGames = wx.getStorageSync('localGames') || {};

            // 更新当前游戏数据
            localGames[gameData.id] = {
                ...gameData,
                lastModified: new Date().toISOString(),
                needsSync: true // 标记需要同步到服务器
            };

            // 保存到本地存储
            wx.setStorageSync('localGames', localGames);

        } catch (error) {
            wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000
            });
        }
    },

    // 从本地存储加载游戏数据
    loadGameFromLocal(gameId) {
        try {
            const localGames = wx.getStorageSync('localGames') || {};
            return localGames[gameId] || null;
        } catch (error) {
            return null;
        }
    },

    // 提交所有本地数据到服务器
    syncLocalDataToServer() {
        try {
            const localGames = wx.getStorageSync('localGames') || {};
            const needsSyncGames = Object.values(localGames).filter(game => game.needsSync);

            if (needsSyncGames.length === 0) {
                return Promise.resolve();
            }

            // 这里可以实现批量同步逻辑
            // 暂时跳过实际的API调用
            return Promise.resolve();
        } catch (error) {
            return Promise.reject(error);
        }
    },

    formatTime(date) {
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const hour = date.getHours();
        const minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
        return `${month}月${day}日 ${hour}:${minute}`;
    },

    showOptions() {
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            showOptionsMenu: true
        });
    },

    hideOptions() {
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            showOptionsMenu: false
        });
    },

    stopPropagation() {
        // 阻止事件冒泡
    },

    showEndGameConfirm() {
        // 隐藏选项菜单
        this.hideOptions();

        // 显示结束游戏确认弹窗
        this.confirmEndGame();
    },

    navigateToHistory() {
        // 隐藏选项菜单
        this.hideOptions();

        // 跳转到历史记录页面（Tab页面）
        wx.switchTab({
            url: '/pages/history/index'
        }).then(() => {
            console.log('跳转到历史记录页面成功');
        }).catch(err => {
            console.error('跳转失败:', err);
            // 如果跳转失败，显示错误提示
            wx.showToast({
                title: '跳转失败: ' + (err.errMsg || '未知错误'),
                icon: 'none',
                duration: 2000
            });
        });
    },



    // 强制样式更新方法
    forceStyleUpdate() {
        // 通过微任务强制重新渲染
        setTimeout(() => {
            this.setData({
                _styleUpdate: Date.now()
            });
        }, 10);
    },



    navigateToSettlement() {
        const {
            game
        } = this.data;

        if (!game) {
            wx.showToast({
                title: '游戏数据无效',
                icon: 'none'
            });
            return;
        }

        if (this.data.isLoading) return;

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            isLoading: true
        });

        // 显示加载中提示
        wx.showLoading({
            title: '结算中...',
            mask: true
        });

        // 调用API结算游戏
        api.games.settle(game.id).then(res => {
            wx.hideLoading();
            console.log('结算游戏成功:', res);

            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                isLoading: false
            });

            // 隐藏选项菜单
            this.hideOptions();

            // 跳转到历史记录页面
            wx.switchTab({
                url: '/pages/history/index',
                success: () => {
                    console.log('成功跳转到历史记录页面');
                },
                fail: (error) => {
                    console.error('跳转失败:', error);
                    // 跳转失败时回退到游戏列表
                    wx.switchTab({
                        url: '/pages/gameList/index'
                    });
                }
            });
        }).catch(err => {
            wx.hideLoading();
            console.error('结算游戏失败:', err);

            wx.showToast({
                title: '结算失败: ' + (err.message || '未知错误'),
                icon: 'none',
                duration: 2000
            });

            this.setData({
                _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                isLoading: false
            });
        });
    },

    deleteGame() {
        const {
            game
        } = this.data;

        if (!game) {
            wx.showToast({
                title: '游戏数据无效',
                icon: 'none'
            });
            return;
        }

        if (this.data.isLoading) return;

        // 显示确认对话框
        wx.showModal({
            title: '确认删除',
            content: `确定要删除游戏"${game.name}"吗？此操作不可恢复。`,
            confirmColor: '#ff4d4f',
            success: (res) => {
                if (res.confirm) {
                    this.setData({
                        _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                        isLoading: true
                    });

                    // 显示加载中提示
                    wx.showLoading({
                        title: '删除中...',
                        mask: true
                    });

                    // 调用API删除游戏
                    api.games.delete(game.id).then(res => {
                        wx.hideLoading();
                        console.log('删除游戏成功:', res);

                        // 显示成功提示
                        wx.showToast({
                            title: '删除成功',
                            icon: 'success',
                            duration: 1500
                        });

                        this.setData({
                            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                            isLoading: false
                        });

                        // 返回到游戏列表页
                        setTimeout(() => {
                            wx.switchTab({
                                url: '/pages/gameList/index'
                            }).then(() => {
                                console.log('跳转到游戏列表页面成功');
                            }).catch(err => {
                                console.error('跳转失败:', err);
                            });
                        }, 1500);
                    }).catch(err => {
                        wx.hideLoading();
                        console.error('删除游戏失败:', err);

                        wx.showToast({
                            title: '删除失败: ' + (err.message || '未知错误'),
                            icon: 'none',
                            duration: 2000
                        });

                        this.setData({
                            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                            isLoading: false
                        });
                    });
                }
            }
        });
    },

    viewRoundDetail(e) {
        const index = e.currentTarget.dataset.index;

        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            showRoundDetail: true,
            selectedRound: index
        });
    },

    hideRoundDetail() {
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            showRoundDetail: false
        });
    },

    // 切换历史记录折叠状态
    toggleRoundCollapse(e) {
        const index = e.currentTarget.dataset.index;
        const {
            game,
            allRoundsCollapsed
        } = this.data;

        if (!game || !game.rounds || index < 0 || index >= game.rounds.length) {
            return;
        }

        // 防止快速连续点击同一个按钮
        const toggleKey = `toggle_${index}`;
        if (this[toggleKey]) {
            return;
        }
        this[toggleKey] = true;

        // 复制游戏数据
        const updatedGame = {
            ...game
        };
        updatedGame.rounds = [...game.rounds];

        // 切换指定轮次的折叠状态
        updatedGame.rounds[index] = {
            ...game.rounds[index],
            collapsed: !game.rounds[index].collapsed
        };

        // 检查是否所有记录都是同一状态，如果是则更新一键折叠状态
        const allCollapsed = updatedGame.rounds.every(round => round.collapsed);
        const allExpanded = updatedGame.rounds.every(round => !round.collapsed);

        let newAllRoundsCollapsed = allRoundsCollapsed;
        if (allCollapsed) {
            newAllRoundsCollapsed = true;
        } else if (allExpanded) {
            newAllRoundsCollapsed = false;
        }

        // 更新数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: updatedGame,
            allRoundsCollapsed: newAllRoundsCollapsed
        });

        // 同时更新本地存储
        this.saveGameToLocal(updatedGame);

        // 保存一键折叠状态
        if (newAllRoundsCollapsed !== allRoundsCollapsed) {
            try {
                wx.setStorageSync('allRoundsCollapsed', newAllRoundsCollapsed);
            } catch (error) {
                console.error('保存一键折叠状态失败:', error);
            }
        }

        // 动画完成后解除锁定
        setTimeout(() => {
            this[toggleKey] = false;
        }, 450); // 略长于动画时长
    },

    // 一键折叠/展开所有历史记录
    toggleAllRoundsCollapse() {
        const {
            allRoundsCollapsed,
            game
        } = this.data;

        if (!game || !game.rounds || game.rounds.length === 0) {
            return;
        }

        // 防止快速连续点击
        if (this.isToggling) {
            return;
        }
        this.isToggling = true;

        const newAllRoundsCollapsed = !allRoundsCollapsed;

        // 复制游戏数据
        const updatedGame = {
            ...game
        };
        updatedGame.rounds = [...game.rounds];

        // 根据一键折叠状态更新所有轮次的折叠状态
        updatedGame.rounds = updatedGame.rounds.map(round => ({
            ...round,
            collapsed: newAllRoundsCollapsed
        }));

        // 更新数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: updatedGame,
            allRoundsCollapsed: newAllRoundsCollapsed
        });

        // 保存到本地存储
        this.saveGameToLocal(updatedGame);

        // 保存一键折叠状态到本地存储
        try {
            wx.setStorageSync('allRoundsCollapsed', newAllRoundsCollapsed);
        } catch (error) {
            console.error('保存一键折叠状态失败:', error);
        }

        // 动画完成后解除锁定
        setTimeout(() => {
            this.isToggling = false;
        }, 500); // 略长于动画时长
    },

    deleteRound() {
        const {
            game,
            selectedRound
        } = this.data;

        if (!game || !game.rounds || selectedRound < 0 || selectedRound >= game.rounds.length) {
            wx.showToast({
                title: '无效的轮次',
                icon: 'none'
            });
            this.hideRoundDetail();
            return;
        }

        const round = game.rounds[selectedRound];

        if (this.data.isLoading) return;

        // 显示确认对话框
        wx.showModal({
            title: '确认删除',
            content: '确定要删除这个轮次吗？此操作不可恢复。',
            confirmColor: '#ff4d4f',
            success: (res) => {
                if (res.confirm) {
                    this.setData({
                        _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                        isLoading: true
                    });

                    // 显示加载中提示
                    wx.showLoading({
                        title: '删除中...',
                        mask: true
                    });

                    // 调用API删除轮次
                    api.games.deleteRound(game.id, round.id).then(res => {
                        wx.hideLoading();
                        console.log('删除轮次成功:', res);

                        // 隐藏轮次详情弹窗
                        this.setData({
                            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                            showRoundDetail: false,
                            isLoading: false
                        });

                        // 显示成功提示
                        wx.showToast({
                            title: '删除成功',
                            icon: 'success',
                            duration: 1500
                        });

                        // 重新加载游戏数据
                        setTimeout(() => {
                            this.loadGame();
                        }, 500);
                    }).catch(err => {
                        wx.hideLoading();
                        console.error('删除轮次失败:', err);

                        wx.showToast({
                            title: '删除失败: ' + (err.message || '未知错误'),
                            icon: 'none',
                            duration: 2000
                        });

                        this.setData({
                            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
                            isLoading: false
                        });
                    });
                }
            }
        });
    },

    /**
     * 重新加载游戏
     */
    reloadGame() {
        wx.showLoading({
            title: '加载中...'
        });

        // 清除当前游戏数据
        this.setData({
            _selectionTimeStamp: Date.now(), // 添加时间戳触发重新渲染
            game: null,
            roundCount: 0,
            currentRoundScores: [],
            selectedPlayers: []
        });

        // 重新加载游戏
        setTimeout(() => {
            this.loadGame();
            wx.hideLoading();
        }, 500);
    },

    /**
     * 确认结束游戏
     */
    confirmEndGame() {
        wx.showModal({
            title: '结束游戏',
            content: '确定要结束本次游戏？',
            confirmColor: '#f5222d',
            success: (res) => {
                if (res.confirm) {
                    // 获取当前游戏数据
                    const {
                        game
                    } = this.data;

                    if (!game) {
                        wx.showToast({
                            title: '游戏数据无效',
                            icon: 'none'
                        });
                        return;
                    }

                    // 结束游戏并调用结算API
                    this.endGameAndSettle(game);
                }
                // 如果取消，则继续停留在记分页面，不需要额外处理
            }
        });
    },

    /**
     * 结束游戏并调用结算API
     */
    endGameAndSettle(game) {
        console.log('开始结束游戏并调用结算API:', game.id);

        // 显示结算中的加载提示
        wx.showLoading({
            title: '结算中...',
            mask: true
        });

        // 调用游戏结算API
        api.games.settle(game.id).then(res => {
            console.log('游戏结算API调用成功:', res);
            wx.hideLoading();

            // 显示结算完成提示
            wx.showToast({
                title: '结算完成',
                icon: 'success',
                duration: 1500
            });

            // 标记游戏为已结束并保存到本地
            const finalGameData = {
                ...game,
                status: 'completed',
                endTime: new Date().toISOString(),
                settlementData: res.data // 保存结算数据
            };
            this.saveGameToLocal(finalGameData);

            // 延迟跳转到结算页面
            setTimeout(() => {
                this.navigateToSettlementPage(game.id);
            }, 1500);

        }).catch(err => {
            console.error('游戏结算API调用失败:', err);
            wx.hideLoading();

            // 根据错误类型显示不同的提示
            let errorMessage = '结算失败';
            let showRetryOption = true;

            if (err.code === 400) {
                errorMessage = '参数错误，请检查游戏数据';
                showRetryOption = false;
            } else if (err.code === 401) {
                errorMessage = '登录已过期，请重新登录';
                showRetryOption = false;
            } else if (err.code === 404) {
                errorMessage = '游戏不存在';
                showRetryOption = false;
            } else if (err.code === 500) {
                errorMessage = '服务器错误，请稍后重试';
            } else if (err.code === -1) {
                errorMessage = '网络连接失败，请检查网络';
            } else if (err.message) {
                errorMessage = err.message;
            }

            // 显示错误处理弹窗
            this.showSettleErrorDialog(errorMessage, showRetryOption, game);
        });
    },

    /**
     * 显示结算错误处理弹窗
     */
    showSettleErrorDialog(errorMessage, showRetryOption, game) {
        const buttons = [];

        if (showRetryOption) {
            buttons.push({
                text: '重试',
                action: () => this.endGameAndSettle(game)
            });
        }

        buttons.push({
            text: '本地结算',
            action: () => this.settleGameLocally(game)
        });

        buttons.push({
            text: '取消',
            action: () => {} // 什么都不做，继续游戏
        });

        // 构建弹窗内容
        let content = errorMessage;
        if (showRetryOption) {
            content += '\n\n您可以重试结算，或选择本地结算继续查看结果。';
        } else {
            content += '\n\n您可以选择本地结算继续查看结果。';
        }

        wx.showModal({
            title: '结算失败',
            content: content,
            showCancel: true,
            confirmText: showRetryOption ? '重试' : '本地结算',
            cancelText: '取消',
            confirmColor: '#1296db',
            success: (res) => {
                if (res.confirm) {
                    if (showRetryOption) {
                        // 重试结算
                        this.endGameAndSettle(game);
                    } else {
                        // 本地结算
                        this.settleGameLocally(game);
                    }
                } else if (res.cancel) {
                    // 用户选择取消，继续游戏
                    console.log('用户取消结算，继续游戏');
                }
            }
        });
    },

    /**
     * 本地结算游戏（API失败时的备选方案）
     */
    settleGameLocally(game) {
        console.log('执行本地结算:', game.id);

        wx.showLoading({
            title: '本地结算中...',
            mask: true
        });

        // 模拟本地结算处理
        setTimeout(() => {
            wx.hideLoading();

            // 标记游戏为已结束并保存到本地
            const finalGameData = {
                ...game,
                status: 'completed',
                endTime: new Date().toISOString(),
                settledLocally: true // 标记为本地结算
            };
            this.saveGameToLocal(finalGameData);

            wx.showToast({
                title: '本地结算完成',
                icon: 'success',
                duration: 1500
            });

            // 跳转到结算页面
            setTimeout(() => {
                this.navigateToSettlementPage(game.id);
            }, 1500);
        }, 800);
    },

    /**
     * 跳转到历史记录页面
     */
    navigateToSettlementPage(gameId) {
        // 跳转到结算页面
        wx.navigateTo({
            url: `/pages/history/gameHistory?id=${gameId}`
        }).then(() => {
            console.log('跳转到游戏历史页面成功');
        }).catch(err => {
            console.error('跳转失败:', err);
            // 如果跳转失败，显示错误提示
            wx.showToast({
                title: '跳转失败: ' + (err.errMsg || '未知错误'),
                icon: 'none',
                duration: 2000
            });

            // 跳转失败时回退到游戏列表
            setTimeout(() => {
                wx.switchTab({
                    url: '/pages/gameList/index'
                });
            }, 2000);
        });
    },

    /**
     * 提交游戏数据到服务器（保留原有方法，用于其他功能）
     */
    submitGameToServer(gameData) {
        return new Promise((resolve, reject) => {
            // 检查是否有可用的API接口
            if (!api || !api.games || !api.games.submitScore) {
                console.log('API接口不可用，跳过服务器提交');
                reject(new Error('API接口不可用'));
                return;
            }

            // 这里可以实现实际的服务器提交逻辑
            // 暂时模拟提交过程
            setTimeout(() => {
                // 模拟网络请求
                const shouldSucceed = Math.random() > 0.3; // 70% 成功率

                if (shouldSucceed) {
                    console.log('模拟服务器提交成功');
                    resolve();
                } else {
                    console.log('模拟服务器提交失败');
                    reject(new Error('网络连接失败'));
                }
            }, 1000);
        });
    },

    /**
     * 生成游戏战绩页面
     */
    generateGameResults(game) {
        // 标记游戏为已结束
        const updatedGame = {
            ...game,
            status: 'completed',
            endTime: Date.now()
        };

        // 更新存储
        const games = wx.getStorageSync('games') || [];
        const updatedGames = games.map(g => g.id === game.id ? updatedGame : g);
        wx.setStorageSync('games', updatedGames);

        // 显示加载提示
        wx.showLoading({
            title: '生成战绩中...'
        });

        // 延迟一小段时间后跳转到战绩页面
        setTimeout(() => {
            wx.hideLoading();

            // 跳转到历史记录页面
            wx.switchTab({
                url: '/pages/history/index',
                success: () => {
                    console.log('成功跳转到历史记录页面');
                },
                fail: (error) => {
                    console.error('跳转失败:', error);
                    // 跳转失败时回退到游戏列表
                    wx.switchTab({
                        url: '/pages/gameList/index'
                    });
                }
            });
        }, 500);
    },

    // ==================== 邀请玩家功能 ====================

    /**
     * 显示邀请玩家弹窗
     */
    showInvitePlayerDialog() {
        // 设置游戏类型名称
        const gameTypeName = this.getGameTypeName(this.data.game.type);

        // 生成邀请码和邀请链接
        const inviteCode = this.generateInviteCode();
        const inviteLink = this.generateInviteLink(inviteCode);

        this.setData({
            _selectionTimeStamp: Date.now(),
            showInvitePlayerModal: true,
            gameTypeName: gameTypeName,
            inviteCode: inviteCode,
            inviteLink: inviteLink
        });
    },

    /**
     * 隐藏邀请玩家弹窗
     */
    hideInvitePlayerDialog() {
        this.setData({
            _selectionTimeStamp: Date.now(),
            showInvitePlayerModal: false
        });
    },

    /**
     * 获取游戏类型名称
     */
    getGameTypeName(gameType) {
        const typeNames = {
            'mahjong': '麻将',
            'poker': '扑克',
            'board': '棋类',
            'other': '其他'
        };
        return typeNames[gameType] || '未知';
    },

    /**
     * 生成邀请码
     */
    generateInviteCode() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        const gameId = this.data.gameId || '';

        // 生成格式：时间戳后6位 + 随机字符串 + 游戏ID后4位
        const inviteCode = timestamp.toString().slice(-6) + random + gameId.slice(-4);
        return inviteCode.toUpperCase();
    },

    /**
     * 生成邀请链接
     */
    generateInviteLink(inviteCode) {
        const gameId = this.data.gameId;
        const baseUrl = '/pages/score/index';
        const params = `?gameId=${gameId}&inviteCode=${inviteCode}&from=invite`;
        return baseUrl + params;
    },

    /**
     * 复制邀请链接
     */
    copyInviteLink() {
        const {
            inviteLink
        } = this.data;

        if (!inviteLink) {
            wx.showToast({
                title: '邀请链接未生成',
                icon: 'none'
            });
            return;
        }

        wx.setClipboardData({
            data: inviteLink,
            success: () => {
                wx.showToast({
                    title: '链接已复制',
                    icon: 'success'
                });
            },
            fail: () => {
                wx.showToast({
                    title: '复制失败',
                    icon: 'none'
                });
            }
        });
    },

    /**
     * 触发微信分享（模态框中的分享按钮）
     */
    triggerWechatShare() {
        const {
            game,
            inviteCode
        } = this.data;

        // 检查游戏状态
        if (!game) {
            wx.showToast({
                title: '游戏数据异常',
                icon: 'none'
            });
            return;
        }

        // 检查游戏是否已满员
        if (game.players && game.players.length >= (game.playerCount || 4)) {
            wx.showModal({
                title: '游戏已满员',
                content: '当前游戏已达到最大人数，确定要分享邀请吗？',
                success: (res) => {
                    if (!res.confirm) {
                        return;
                    }
                    this.prepareAndShare();
                }
            });
            return;
        }

        this.prepareAndShare();
    },

    /**
     * 准备分享数据并执行分享
     */
    prepareAndShare() {
        // 确保邀请码已生成
        if (!this.data.inviteCode) {
            const inviteCode = this.generateInviteCode();
            const inviteLink = this.generateInviteLink(inviteCode);

            this.setData({
                _selectionTimeStamp: Date.now(),
                inviteCode: inviteCode,
                inviteLink: inviteLink
            });
        }

        // 显示分享提示
        wx.showToast({
            title: '请选择分享对象',
            icon: 'none',
            duration: 1500
        });

        // 注意：微信小程序中，open-type="share" 的按钮会自动触发 onShareAppMessage
        // 这里主要是确保数据准备就绪和用户反馈
    },

    /**
     * 阻止事件冒泡（防止弹窗意外关闭）
     */
    stopPropagation() {
        // 空函数，用于阻止事件冒泡
        // 在微信小程序中，catchtap 会自动阻止事件冒泡
        return false;
    },

    /**
     * 处理邀请加入游戏
     */
    handleInviteJoin(inviteCode) {
        wx.showLoading({
            title: '验证邀请码...',
            mask: true
        });

        // 验证邀请码有效性
        this.validateInviteCode(inviteCode).then(() => {
            // 邀请码有效，加载游戏并尝试加入
            this.loadGame().then(() => {
                this.joinGameAsInvitedPlayer();
            });
        }).catch(error => {
            wx.hideLoading();
            wx.showModal({
                title: '邀请无效',
                content: error.message || '邀请链接已失效或游戏不存在',
                showCancel: false,
                confirmText: '返回',
                success: () => {
                    wx.switchTab({
                        url: '/pages/gameList/index'
                    });
                }
            });
        });
    },

    /**
     * 验证邀请码有效性
     */
    validateInviteCode(inviteCode) {
        return new Promise((resolve, reject) => {
            const {
                gameId
            } = this.data;

            // 基本格式验证
            if (!inviteCode || inviteCode.length < 10) {
                reject(new Error('邀请码格式无效'));
                return;
            }

            // 检查邀请码是否过期（这里简单检查时间戳，实际应该调用API）
            const timestampPart = inviteCode.substring(0, 6);
            const inviteTime = parseInt('1' + timestampPart + '000000'); // 简单的时间戳重构
            const currentTime = Date.now();
            const expireTime = 24 * 60 * 60 * 1000; // 24小时过期

            if (currentTime - inviteTime > expireTime) {
                reject(new Error('邀请链接已过期'));
                return;
            }

            // 验证邀请码是否与游戏ID匹配
            const gameIdPart = inviteCode.slice(-4);
            const actualGameIdPart = gameId.slice(-4);
            if (gameIdPart !== actualGameIdPart) {
                reject(new Error('邀请码与游戏不匹配'));
                return;
            }

            // 检查邀请码是否已被使用（防止重复使用）
            const usedInviteCodes = wx.getStorageSync('usedInviteCodes') || [];
            if (usedInviteCodes.includes(inviteCode)) {
                reject(new Error('邀请码已被使用'));
                return;
            }

            // TODO: 这里应该调用后端API验证邀请码
            // api.games.validateInvite(gameId, inviteCode).then(resolve).catch(reject);

            // 临时的本地验证逻辑
            setTimeout(() => {
                // 标记邀请码为已使用
                usedInviteCodes.push(inviteCode);
                wx.setStorageSync('usedInviteCodes', usedInviteCodes);
                resolve();
            }, 1000);
        });
    },

    /**
     * 作为被邀请玩家加入游戏
     */
    joinGameAsInvitedPlayer() {
        const {
            game
        } = this.data;

        if (!game) {
            wx.hideLoading();
            wx.showToast({
                title: '游戏不存在',
                icon: 'none'
            });
            return;
        }

        // 检查游戏是否已满员
        if (game.players && game.players.length >= (game.playerCount || 4)) {
            wx.hideLoading();
            wx.showModal({
                title: '游戏已满员',
                content: '该游戏已达到最大人数限制',
                showCancel: false,
                confirmText: '确定'
            });
            return;
        }

        // 获取当前用户信息
        const userInfo = wx.getStorageSync('userInfo') || {
            nickName: `玩家${Date.now().toString().slice(-4)}`,
            avatarUrl: '/static/images/default-avatar.png'
        };

        // 检查游戏是否已结束
        if (game.settled) {
            wx.hideLoading();
            wx.showModal({
                title: '游戏已结束',
                content: '该游戏已经结束，无法加入',
                showCancel: false,
                confirmText: '确定'
            });
            return;
        }

        // 检查是否已经在游戏中（多重验证）
        const existingPlayer = game.players.find(player =>
            player.name === userInfo.nickName ||
            player.id === userInfo.id ||
            (player.openId && userInfo.openId && player.openId === userInfo.openId)
        );

        if (existingPlayer) {
            wx.hideLoading();
            wx.showToast({
                title: '您已在游戏中',
                icon: 'success'
            });
            return;
        }

        // 检查用户身份验证
        if (!this.validatePlayerIdentity(userInfo)) {
            wx.hideLoading();
            wx.showModal({
                title: '身份验证失败',
                content: '请先完善用户信息',
                showCancel: false,
                confirmText: '确定'
            });
            return;
        }

        // 创建新玩家对象
        const newPlayer = {
            id: `player_${Date.now()}`,
            name: userInfo.nickName,
            avatar: userInfo.avatarUrl,
            score: game.initialScore || 0,
            selected: false,
            isInvited: true // 标记为被邀请玩家
        };

        // 更新游戏数据
        const updatedGame = {
            ...game,
            players: [...game.players, newPlayer]
        };

        // 保存到本地存储
        this.saveGameToLocal(updatedGame);

        // 更新页面数据
        this.setData({
            _selectionTimeStamp: Date.now(),
            game: updatedGame,
            currentRoundScores: [...this.data.currentRoundScores, 0]
        });

        wx.hideLoading();

        // 显示欢迎提示和游戏状态概览
        const gameStatus = this.getGameStatusSummary(updatedGame);
        wx.showModal({
            title: '加入成功',
            content: `欢迎加入「${game.name}」！\n\n${gameStatus}`,
            showCancel: false,
            confirmText: '开始游戏'
        });

        // TODO: 这里应该调用API通知其他玩家有新玩家加入
        // api.games.joinGame(game.id, newPlayer).then(...).catch(...);
    },

    /**
     * 获取游戏状态概览
     */
    getGameStatusSummary(game) {
        const playerCount = game.players.length;
        const maxPlayers = game.playerCount || 4;
        const roundCount = game.rounds ? game.rounds.length : 0;

        let summary = `当前人数：${playerCount}/${maxPlayers}人\n`;
        summary += `已进行轮次：${roundCount}轮\n`;

        if (game.players.length > 0) {
            const scores = game.players.map(p => p.score);
            const maxScore = Math.max(...scores);
            const minScore = Math.min(...scores);
            const leader = game.players.find(p => p.score === maxScore);

            if (roundCount > 0) {
                summary += `当前领先：${leader.name}（${maxScore}分）`;
            } else {
                summary += `游戏尚未开始，快来一起玩吧！`;
            }
        }

        return summary;
    },

    /**
     * 验证玩家身份
     */
    validatePlayerIdentity(userInfo) {
        // 检查必要的用户信息
        if (!userInfo.nickName || userInfo.nickName.trim() === '') {
            return false;
        }

        // 检查昵称长度
        if (userInfo.nickName.length > 20) {
            return false;
        }

        // 检查昵称是否包含敏感词（简单示例）
        const sensitiveWords = ['管理员', 'admin', '系统'];
        const lowerNickName = userInfo.nickName.toLowerCase();
        for (const word of sensitiveWords) {
            if (lowerNickName.includes(word)) {
                return false;
            }
        }

        return true;
    },

    /**
     * 分享配置 - 用于邀请玩家加入游戏
     */
    onShareAppMessage() {
        const {
            game,
            inviteLink,
            inviteCode,
            showInvitePlayerModal
        } = this.data;

        if (!game) {
            return {
                title: '邀请你加入游戏',
                path: '/pages/gameList/index',
                imageUrl: '/static/images/logo.png'
            };
        }

        // 如果正在显示邀请模态框或已有邀请链接，优先使用邀请功能
        if (showInvitePlayerModal || inviteLink) {
            // 如果邀请码尚未生成，先生成邀请码
            let currentInviteCode = inviteCode;
            let currentInviteLink = inviteLink;

            if (!currentInviteCode) {
                currentInviteCode = this.generateInviteCode();
                currentInviteLink = this.generateInviteLink(currentInviteCode);

                // 更新数据，但不触发重新渲染
                this.data.inviteCode = currentInviteCode;
                this.data.inviteLink = currentInviteLink;
            }

            // 构建分享内容
            const shareTitle = `邀请你加入「${game.name}」`;
            const shareDesc = this.generateShareDescription(game);

            return {
                title: shareTitle,
                path: currentInviteLink,
                imageUrl: '/static/images/logo.png',
                desc: shareDesc
            };
        }

        // 默认分享（非邀请模式）
        const gameTypeName = this.getGameTypeName(game.type);
        const playerCount = game.players ? game.players.length : 0;
        const maxPlayers = game.playerCount || 4;

        return {
            title: `「${game.name}」游戏进行中`,
            path: `/pages/score/index?gameId=${game.id}&from=share`,
            imageUrl: '/static/images/logo.png',
            desc: `${gameTypeName} · ${playerCount}/${maxPlayers}人 · 快来围观！`
        };
    },

    /**
     * 生成分享描述
     */
    generateShareDescription(game) {
        const gameTypeName = this.getGameTypeName(game.type);
        const playerCount = game.players ? game.players.length : 0;
        const maxPlayers = game.playerCount || 4;
        const roundCount = game.rounds ? game.rounds.length : 0;

        let desc = `${gameTypeName}游戏`;

        if (roundCount > 0) {
            desc += ` · 已进行${roundCount}轮`;

            // 如果有玩家数据，显示当前领先者
            if (game.players && game.players.length > 0) {
                const scores = game.players.map(p => p.score);
                const maxScore = Math.max(...scores);
                const leader = game.players.find(p => p.score === maxScore);
                if (leader) {
                    desc += ` · ${leader.name}领先`;
                }
            }
        } else {
            desc += ` · 等待开始`;
        }

        desc += ` · ${playerCount}/${maxPlayers}人`;

        return desc;
    },

    /**
     * 分享成功回调
     */
    onShareAppMessageSuccess(res) {
        // 分享成功后的处理
        wx.showToast({
            title: '分享成功',
            icon: 'success',
            duration: 2000
        });

        // 如果是邀请模式的分享，可以记录分享次数
        if (this.data.showInvitePlayerModal && this.data.inviteCode) {
            // TODO: 可以调用API记录邀请分享次数
            // api.games.recordInviteShare(this.data.gameId, this.data.inviteCode);
        }
    },

    /**
     * 分享失败回调
     */
    onShareAppMessageFail(res) {
        // 分享失败的处理
        wx.showToast({
            title: '分享失败',
            icon: 'none',
            duration: 2000
        });
    }
})