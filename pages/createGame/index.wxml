<!--createGame.wxml-->
<view class="container">
  <!-- 背景装饰元素 -->
  <view class="bg-elements">
    <view class="mahjong-tile mahjong-1"></view>
    <view class="mahjong-tile mahjong-2"></view>
    <view class="mahjong-tile mahjong-3"></view>
    <view class="mahjong-tile mahjong-4"></view>
  </view>
  
  <!-- 自定义导航栏 -->
  <custom-navbar
    title="创建新游戏"
    show-back="{{true}}"
    show-home="{{true}}"
    bind:back="onNavBack"
    bind:home="onNavHome"
  ></custom-navbar>
  
  <!-- 可滚动内容区域 -->
  <scroll-view class="scroll-content" scroll-y="true" style="height: calc(100vh - {{navbarHeight}}px - 100rpx); margin-top: {{navbarHeight}}px;">
    <view class="content">
      <view class="form-card">
        <view class="form-title">
          <view class="mahjong-section-title">游戏设置</view>
        </view>
        
        <view class="form-group">
          <view class="form-label">游戏名称</view>
          <input class="form-input" placeholder="请输入游戏名称" value="{{gameName}}" bindinput="onGameNameInput" />
        </view>

        <view class="form-group">
          <view class="form-label">游戏类型</view>
          <view class="game-types">
            <view class="game-type-item {{gameType === 'mahjong' ? 'active' : ''}}" bindtap="selectGameType" data-type="mahjong">
              <view class="type-icon">🀄</view>
              <text class="type-name">麻将</text>
            </view>
            <view class="game-type-item {{gameType === 'poker' ? 'active' : ''}}" bindtap="selectGameType" data-type="poker">
              <view class="type-icon">🃏</view>
              <text class="type-name">扑克</text>
            </view>
            <view class="game-type-item {{gameType === 'board' ? 'active' : ''}}" bindtap="selectGameType" data-type="board">
              <view class="type-icon">♟️</view>
              <text class="type-name">棋类</text>
            </view>
            <view class="game-type-item {{gameType === 'other' ? 'active' : ''}}" bindtap="selectGameType" data-type="other">
              <view class="type-icon">🎮</view>
              <text class="type-name">其他</text>
            </view>
          </view>
        </view>

        <view class="form-group">
          <view class="form-label">玩家选择方式</view>
          <view class="player-mode-selector">
            <view class="mode-item {{playerMode === 'local' ? 'active' : ''}}" bindtap="selectPlayerMode" data-mode="local">
              <view class="mode-icon">👥</view>
              <view class="mode-content">
                <text class="mode-name">本地玩家</text>
                <text class="mode-desc">在同一设备上设置玩家</text>
              </view>
            </view>
            <view class="mode-item {{playerMode === 'invite' ? 'active' : ''}}" bindtap="selectPlayerMode" data-mode="invite">
              <view class="mode-icon">📱</view>
              <view class="mode-content">
                <text class="mode-name">邀请好友</text>
                <text class="mode-desc">邀请好友加入游戏</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 玩家人数选择区域 - 仅在本地玩家模式下显示 -->
        <view class="form-group" wx:if="{{playerMode === 'local'}}">
          <view class="form-label">玩家人数</view>
          <view class="player-count-selector">
            <view class="count-item {{playerCount === 2 ? 'active' : ''}}" bindtap="selectPlayerCount" data-count="2">2人</view>
            <view class="count-item {{playerCount === 3 ? 'active' : ''}}" bindtap="selectPlayerCount" data-count="3">3人</view>
            <view class="count-item {{playerCount === 4 ? 'active' : ''}}" bindtap="selectPlayerCount" data-count="4">4人</view>
            <view class="count-item {{isCustomPlayerCount ? 'active' : ''}}" bindtap="selectCustomPlayerCount">自定义</view>
          </view>

          <!-- 自定义人数输入框 -->
          <view class="custom-count-input {{isCustomPlayerCount ? 'show' : ''}}" wx:if="{{isCustomPlayerCount}}">
            <input
              class="form-input custom-input"
              type="number"
              placeholder="请输入玩家人数 (2-20)"
              value="{{customPlayerCount}}"
              bindinput="onCustomPlayerCountInput"
              bindfocus="onCustomPlayerCountFocus"
              bindblur="onCustomPlayerCountBlur"
              maxlength="2"
              focus="{{isCustomInputFocused}}"
            />
            <view class="input-tip">建议玩家人数：2-20人</view>
          </view>
        </view>

        <view class="form-group">
          <view class="form-label">初始分数</view>
          <input class="form-input" type="number" placeholder="请输入初始分数" value="{{initialScore}}" bindinput="onInitialScoreInput" />
        </view>
      </view>

      <!-- 移除原来的action-buttons，为底部留出空间 -->
      <view class="bottom-spacer"></view>
    </view>
  </scroll-view>

  <!-- 底部固定导航栏 -->
  <view class="bottom-nav-bar">
    <button class="next-btn" bindtap="navigateToPlayers" disabled="{{!canProceed}}">
      下一步
    </button>
  </view>
</view> 