// createGame.js
const router = require('../../utils/router');

Component({
    options: {
        styleIsolation: 'apply-shared'
    },

    data: {
        navbarHeight: 88, // 导航栏高度，会在页面加载时更新
        gameName: '',
        gameType: 'mahjong',
        playerMode: 'local', // 玩家选择方式：local(本地) / invite(邀请好友)
        playerCount: 4,
        isCustomPlayerCount: false, // 是否选择自定义人数
        customPlayerCount: '', // 自定义人数值
        initialScore: '0',
        canProceed: false,
        isCustomInputFocused: false // 添加焦点状态跟踪
    },

    lifetimes: {
        attached() {
            // 设置默认游戏名称
            const date = new Date();
            const defaultName = `${date.getMonth() + 1}月${date.getDate()}日游戏`;

            // 根据游戏类型设置默认初始分数
            const defaultScore = this.getDefaultScore('mahjong');

            this.setData({
                gameName: defaultName,
                initialScore: defaultScore,
                canProceed: true
            });

            // 获取导航栏高度
            this.getNavbarHeight();
        },

        // 添加空函数以解决autofillHideDropdown错误
        detached() {
            // 空函数，处理组件卸载
        }
    },

    methods: {
        // 添加空函数以解决autofillHideDropdown错误
        autofillHideDropdown() {
            // 这是一个空函数，用于防止组件调用不存在的方法时报错
            console.log('autofillHideDropdown called');
        },

        // 获取导航栏高度
        getNavbarHeight() {
            // 延迟获取，确保组件已经初始化
            setTimeout(() => {
                try {
                    const navbar = this.selectComponent('custom-navbar');
                    if (navbar) {
                        const height = navbar.getNavbarHeight();
                        this.setData({
                            navbarHeight: height
                        });
                        console.log('获取到导航栏高度:', height);
                    }
                } catch (error) {
                    console.error('获取导航栏高度失败:', error);
                }
            }, 100);
        },

        // 导航栏返回事件
        onNavBack(e) {
            router.navigateBack();
        },

        // 导航栏首页事件
        onNavHome(e) {
            router.switchTab('gameList');
        },

        onGameNameInput(e) {
            this.setData({
                gameName: e.detail.value
            });
            this.checkCanProceed();
        },

        selectGameType(e) {
            const type = e.currentTarget.dataset.type;
            const defaultScore = this.getDefaultScore(type);

            this.setData({
                gameType: type,
                initialScore: defaultScore
            });
            this.checkCanProceed();
        },

        selectPlayerCount(e) {
            // 通过延时解决生命周期问题
            setTimeout(() => {
                this.setData({
                    playerCount: parseInt(e.currentTarget.dataset.count),
                    isCustomPlayerCount: false,
                    customPlayerCount: '',
                    isCustomInputFocused: false // 重置焦点状态
                });
                this.checkCanProceed();
            }, 50);
        },

        selectCustomPlayerCount() {
            // 默认值设置为5人，如果之前有输入则保留
            const count = this.data.customPlayerCount ?
                parseInt(this.data.customPlayerCount) : 5;

            // 通过延时解决生命周期问题
            setTimeout(() => {
                this.setData({
                    isCustomPlayerCount: true,
                    playerCount: count,
                    customPlayerCount: count.toString(),
                    isCustomInputFocused: true // 设置焦点状态
                });
                this.checkCanProceed();
            }, 50);
        },

        onCustomPlayerCountFocus() {
            this.setData({
                isCustomInputFocused: true
            });
        },

        onCustomPlayerCountBlur(e) {
            // 由于可能存在生命周期问题，我们通过setTimeout来避免
            setTimeout(() => {
                this.setData({
                    isCustomInputFocused: false
                });

                const value = e.detail.value;
                if (value) {
                    let count = parseInt(value);
                    if (count < 2) {
                        count = 2;
                        this.setData({
                            customPlayerCount: '2',
                            playerCount: 2
                        });
                        wx.showToast({
                            title: '最少需要2人',
                            icon: 'none'
                        });
                    } else if (count > 20) {
                        count = 20;
                        this.setData({
                            customPlayerCount: '20',
                            playerCount: 20
                        });
                        wx.showToast({
                            title: '最多支持20人',
                            icon: 'none'
                        });
                    } else {
                        // 确保playerCount与输入值同步
                        this.setData({
                            playerCount: count
                        });
                    }
                } else {
                    // 如果输入为空，默认设置为2人
                    this.setData({
                        customPlayerCount: '2',
                        playerCount: 2
                    });
                }

                this.checkCanProceed();
            }, 50);
        },

        onCustomPlayerCountInput(e) {
            const value = e.detail.value;

            // 只允许输入数字
            if (value && /^\d+$/.test(value)) {
                const count = parseInt(value);
                this.setData({
                    customPlayerCount: value,
                    playerCount: count
                });
            } else if (value === '') {
                // 允许清空输入
                this.setData({
                    customPlayerCount: ''
                });
            } else {
                // 如果输入非数字，保持原值不变
                return;
            }

            this.checkCanProceed();
        },

        selectPlayerMode(e) {
            this.setData({
                playerMode: e.currentTarget.dataset.mode
            });
            this.checkCanProceed();
        },

        onInitialScoreInput(e) {
            this.setData({
                initialScore: e.detail.value
            });
            this.checkCanProceed();
        },

        getDefaultScore(gameType) {
            switch (gameType) {
                case 'mahjong':
                    return '1000';
                case 'poker':
                    return '500';
                case 'board':
                    return '100';
                case 'other':
                    return '0';
                default:
                    return '0';
            }
        },

        checkCanProceed() {
            const {
                gameName,
                initialScore,
                isCustomPlayerCount,
                customPlayerCount,
                playerCount
            } = this.data;

            // 基础验证：游戏名称和初始分数不能为空
            let isValid = gameName.trim() !== '' && initialScore !== '';

            // 如果是自定义人数，需要额外验证自定义人数
            if (isCustomPlayerCount) {
                const count = parseInt(customPlayerCount);
                isValid = isValid &&
                    customPlayerCount !== '' &&
                    !isNaN(count) &&
                    count >= 2 &&
                    count <= 20;
            }

            this.setData({
                canProceed: isValid
            });
        },

        navigateToPlayers() {
            const {
                gameName,
                gameType,
                playerMode,
                playerCount,
                initialScore
            } = this.data;

            // 创建游戏基本信息
            const gameInfo = {
                name: gameName,
                type: gameType,
                mode: playerMode,
                playerCount: playerCount,
                initialScore: parseInt(initialScore) || 0,
                players: []
            };

            // 存储到全局数据或临时存储
            wx.setStorageSync('currentGameInfo', gameInfo);

            // 根据玩家模式跳转到不同页面
            if (playerMode === 'local') {
                // 本地玩家模式，跳转到玩家设置页面
                router.navigateTo('/pages/players/index');
            } else if (playerMode === 'invite') {
                // 邀请好友模式，直接跳转到记分页面
                // 首先创建游戏并获取游戏ID，然后跳转到记分页面
                this.createInviteGame(gameInfo);
            }
        },

        // 创建邀请模式的游戏
        createInviteGame(gameInfo) {
            // 显示加载提示
            wx.showLoading({
                title: '创建游戏中...',
                mask: true
            });

            // 获取当前用户信息作为游戏创建人
            const userInfo = wx.getStorageSync('userInfo') || {
                nickName: '游戏创建人',
                avatarUrl: '/static/images/default-avatar.png'
            };

            // 创建游戏数据，包含创建人作为第一个玩家
            const gameData = {
                ...gameInfo,
                id: Date.now().toString(),
                createTime: Date.now(),
                players: [{
                    id: `player_${Date.now()}`,
                    name: userInfo.nickName,
                    avatar: userInfo.avatarUrl,
                    score: gameInfo.initialScore,
                    selected: false,
                    isCreator: true // 标记为创建人
                }],
                rounds: [],
                settled: false
            };

            // 保存游戏数据到本地
            const games = wx.getStorageSync('games') || [];
            games.unshift(gameData);
            wx.setStorageSync('games', games);

            // 清除临时存储的游戏信息
            wx.removeStorageSync('currentGameInfo');

            wx.hideLoading();

            // 跳转到记分页面
            router.redirectTo('score', {
                id: gameData.id
            });
        }
    }
})